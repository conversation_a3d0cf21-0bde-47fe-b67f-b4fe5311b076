/**
 * 博客分类页面Ajax无感分页功能
 * 实现点击分页链接时不刷新页面，通过Ajax获取数据并更新DOM
 */

(function() {
    'use strict';

    // 分页配置
    const PAGINATION_CONFIG = {
        containerSelector: '#blog-cate-table-container',
        paginationSelector: '.pager_box',
        ajaxUrl: '/Blog/Cate?handler=AjaxPage',
        loadingClass: 'ajax-loading'
    };

    // 当前分页状态
    let currentState = {
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        pageSize: 10,
        filters: {
            keyword: ''
        }
    };

    /**
     * 初始化Ajax分页功能
     */
    function initAjaxPagination() {
        // 从页面获取初始状态
        updateStateFromPage();
        
        // 绑定分页链接点击事件
        bindPaginationEvents();
        
        // 绑定页码跳转事件
        bindJumpPageEvents();
        
        console.log('博客分类Ajax分页功能已初始化');
    }

    /**
     * 从页面元素获取当前状态
     */
    function updateStateFromPage() {
        // 从分页信息中获取状态
        const paginationInfo = document.querySelector('.pager .info');
        if (paginationInfo) {
            const infoText = paginationInfo.textContent;
            const matches = infoText.match(/共\s*(\d+)\s*条记录，(\d+)\s*页/);
            if (matches) {
                currentState.totalCount = parseInt(matches[1]);
                currentState.totalPages = parseInt(matches[2]);
            }
        }

        // 获取当前页码
        const currentPageElement = document.querySelector('.pager .current');
        if (currentPageElement) {
            currentState.currentPage = parseInt(currentPageElement.textContent) || 1;
        }

        // 从URL参数获取筛选条件
        const urlParams = new URLSearchParams(window.location.search);
        currentState.filters = {
            keyword: urlParams.get('Keyword') || ''
        };

        // 获取页面大小
        const pageSizeFromUrl = urlParams.get('PageSize');
        if (pageSizeFromUrl) {
            currentState.pageSize = parseInt(pageSizeFromUrl);
        }
    }

    /**
     * 绑定分页链接点击事件
     */
    function bindPaginationEvents() {
        // 使用事件委托，确保动态生成的分页链接也能响应
        document.addEventListener('click', function(e) {
            const target = e.target;
            
            // 检查是否是分页链接
            if (target.matches('.pager a') || target.closest('.pager a')) {
                e.preventDefault();
                
                const link = target.matches('.pager a') ? target : target.closest('.pager a');
                const href = link.getAttribute('href');
                
                if (href && href !== '#' && href !== 'javascript:;') {
                    // 从链接中解析页码
                    const pageMatch = href.match(/[?&]CurrentPage=(\d+)/);
                    if (pageMatch) {
                        const targetPage = parseInt(pageMatch[1]);
                        loadPage(targetPage);
                    }
                }
                
                return false;
            }
        });
    }

    /**
     * 绑定页码跳转事件
     */
    function bindJumpPageEvents() {
        // 跳转按钮点击事件
        document.addEventListener('click', function(e) {
            if (e.target.matches('#jump-page-btn')) {
                e.preventDefault();
                
                const pageInput = document.getElementById('jump-page-input');
                if (pageInput) {
                    const pageNum = parseInt(pageInput.value);
                    const maxPage = parseInt(pageInput.getAttribute('max')) || currentState.totalPages;
                    
                    // 验证页码
                    let targetPage = pageNum;
                    if (isNaN(targetPage) || targetPage < 1) {
                        targetPage = 1;
                    } else if (targetPage > maxPage) {
                        targetPage = maxPage;
                    }
                    
                    loadPage(targetPage);
                }
                
                return false;
            }
        });

        // 输入框回车事件
        document.addEventListener('keypress', function(e) {
            if (e.target.matches('#jump-page-input') && e.which === 13) {
                e.preventDefault();
                document.getElementById('jump-page-btn')?.click();
            }
        });
    }

    /**
     * 加载指定页码的数据
     * @param {number} pageNum 目标页码
     */
    function loadPage(pageNum) {
        if (pageNum === currentState.currentPage) {
            return; // 如果是当前页，不需要重新加载
        }

        // 显示加载状态
        showLoading();

        // 构建请求参数
        const params = new URLSearchParams();
        params.set('PageIndex', pageNum);
        params.set('PageSize', currentState.pageSize);
        
        // 添加筛选条件
        if (currentState.filters.keyword) {
            params.set('Keyword', currentState.filters.keyword);
        }

        // 发送Ajax请求
        const url = `${PAGINATION_CONFIG.ajaxUrl}&${params.toString()}`;
        
        fetch(url, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 更新页面内容
                updatePageContent(data);
                
                // 更新状态
                currentState.currentPage = data.pagination.currentPage;
                currentState.totalPages = data.pagination.totalPages;
                currentState.totalCount = data.pagination.totalCount;
                currentState.pageSize = data.pagination.pageSize;
                currentState.filters = data.filters;
                
                // 更新浏览器URL
                updateBrowserUrl(pageNum);
                
                console.log(`已加载第 ${pageNum} 页数据`);
            } else {
                throw new Error(data.message || '加载数据失败');
            }
        })
        .catch(error => {
            console.error('Ajax分页请求失败:', error);
            showError('加载数据失败，请稍后重试');
        })
        .finally(() => {
            hideLoading();
        });
    }

    /**
     * 更新页面内容
     * @param {Object} data 服务器返回的数据
     */
    function updatePageContent(data) {
        // 更新表格内容
        const container = document.querySelector(PAGINATION_CONFIG.containerSelector);
        if (container && data.html) {
            container.innerHTML = data.html;
        }

        // 重新生成分页控件
        generatePaginationControls(data.pagination);
        
        // 重新初始化表格相关功能（如果需要）
        reinitializeTableFeatures();
    }

    /**
     * 生成分页控件HTML
     * @param {Object} pagination 分页信息
     */
    function generatePaginationControls(pagination) {
        const paginationContainer = document.querySelector(PAGINATION_CONFIG.paginationSelector);
        if (!paginationContainer || pagination.totalPages <= 0) {
            return;
        }

        const { currentPage, totalPages, totalCount, pageSize } = pagination;
        
        // 计算分页显示逻辑
        let startPage = 1;
        let endPage = totalPages;
        let showStartEllipsis = false;
        let showEndEllipsis = false;

        if (totalPages > 7) {
            if (currentPage <= 4) {
                startPage = 1;
                endPage = 5;
                showEndEllipsis = true;
            } else if (currentPage >= totalPages - 3) {
                startPage = totalPages - 4;
                endPage = totalPages;
                showStartEllipsis = true;
            } else {
                startPage = currentPage - 2;
                endPage = currentPage + 2;
                showStartEllipsis = true;
                showEndEllipsis = true;
            }
        }

        // 构建分页HTML
        let paginationHtml = '<div class="pager">';

        // 上一页
        if (currentPage > 1) {
            paginationHtml += `<a href="#" data-page="${currentPage - 1}" class="prev">上一页</a>`;
        }

        // 第一页和省略号
        if (showStartEllipsis) {
            if (currentPage === 1) {
                paginationHtml += '<span class="current">1</span>';
            } else {
                paginationHtml += '<a href="#" data-page="1">1</a>';
            }
            paginationHtml += '<span class="ellipsis">...</span>';
        }

        // 页码范围
        for (let i = startPage; i <= endPage; i++) {
            if (i === currentPage) {
                paginationHtml += `<span class="current">${i}</span>`;
            } else {
                paginationHtml += `<a href="#" data-page="${i}">${i}</a>`;
            }
        }

        // 最后一页和省略号
        if (showEndEllipsis) {
            paginationHtml += '<span class="ellipsis">...</span>';
            if (currentPage === totalPages) {
                paginationHtml += `<span class="current">${totalPages}</span>`;
            } else {
                paginationHtml += `<a href="#" data-page="${totalPages}">${totalPages}</a>`;
            }
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `<a href="#" data-page="${currentPage + 1}" class="next">下一页</a>`;
        }

        // 页码跳转
        paginationHtml += `
            <span class="page-jump">
                跳转到
                <input type="number" id="jump-page-input" min="1" max="${totalPages}" value="${currentPage}" style="width: 50px; text-align: center; margin: 0 5px;">
                页
                <button type="button" id="jump-page-btn" style="margin-left: 5px; padding: 4px 8px;">跳转</button>
            </span>
        `;

        // 统计信息
        paginationHtml += `<span class="info">共 ${totalCount} 条记录，${totalPages} 页</span>`;
        paginationHtml += '</div>';

        paginationContainer.innerHTML = paginationHtml;

        // 绑定新生成的分页链接事件
        bindGeneratedPaginationEvents();
    }

    /**
     * 绑定动态生成的分页链接事件
     */
    function bindGeneratedPaginationEvents() {
        const paginationContainer = document.querySelector(PAGINATION_CONFIG.paginationSelector);
        if (!paginationContainer) return;

        // 为动态生成的分页链接绑定点击事件
        paginationContainer.addEventListener('click', function(e) {
            if (e.target.matches('a[data-page]')) {
                e.preventDefault();
                const targetPage = parseInt(e.target.getAttribute('data-page'));
                if (targetPage && targetPage !== currentState.currentPage) {
                    loadPage(targetPage);
                }
                return false;
            }
        });
    }

    /**
     * 更新浏览器URL（不刷新页面）
     * @param {number} pageNum 页码
     */
    function updateBrowserUrl(pageNum) {
        const url = new URL(window.location);
        url.searchParams.set('CurrentPage', pageNum);
        
        // 保持关键词筛选参数
        if (currentState.filters.keyword) {
            url.searchParams.set('Keyword', currentState.filters.keyword);
        } else {
            url.searchParams.delete('Keyword');
        }

        // 使用pushState更新URL，不刷新页面
        window.history.pushState({ page: pageNum }, '', url.toString());
    }

    /**
     * 显示加载状态
     */
    function showLoading() {
        const container = document.querySelector(PAGINATION_CONFIG.containerSelector);
        if (container) {
            container.classList.add(PAGINATION_CONFIG.loadingClass);
            
            // 添加加载遮罩
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'ajax-loading-overlay';
            loadingOverlay.innerHTML = '<div class="loading-spinner">加载中...</div>';
            container.style.position = 'relative';
            container.appendChild(loadingOverlay);
        }
    }

    /**
     * 隐藏加载状态
     */
    function hideLoading() {
        const container = document.querySelector(PAGINATION_CONFIG.containerSelector);
        if (container) {
            container.classList.remove(PAGINATION_CONFIG.loadingClass);
            
            // 移除加载遮罩
            const loadingOverlay = container.querySelector('.ajax-loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.remove();
            }
        }
    }

    /**
     * 显示错误信息
     * @param {string} message 错误信息
     */
    function showError(message) {
        // 这里可以使用项目中的通知组件
        console.error(message);
        alert(message); // 临时使用alert，可以替换为更好的UI组件
    }

    /**
     * 重新初始化表格相关功能
     */
    function reinitializeTableFeatures() {
        // 重新初始化博客分类相关功能
        if (typeof blog_obj !== 'undefined' && blog_obj.blog_category_init) {
            blog_obj.blog_category_init();
        }

        console.log('分类表格功能已重新初始化');
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAjaxPagination);
    } else {
        initAjaxPagination();
    }

    // 处理浏览器前进后退
    window.addEventListener('popstate', function(e) {
        if (e.state && e.state.page) {
            loadPage(e.state.page);
        } else {
            // 如果没有状态信息，重新加载页面
            window.location.reload();
        }
    });

    // 导出到全局作用域（如果需要）
    window.BlogCateAjaxPagination = {
        loadPage: loadPage,
        getCurrentState: () => ({ ...currentState })
    };

})();
