# 博客页面Ajax无感分页功能

## 功能概述

为博客页面和博客分类页面实现了Ajax无感分页功能，用户点击分页链接时不会刷新整个页面，而是通过Ajax请求获取新的数据并动态更新页面内容。

## 实现的功能

### 1. 无感分页
- 点击分页链接时不刷新页面
- 通过Ajax请求获取新的分页数据
- 动态更新表格内容和分页控件
- 保持所有筛选条件

### 2. URL同步
- 使用HTML5 History API更新浏览器URL
- 支持浏览器前进后退按钮
- URL参数与页面状态保持同步

### 3. 加载状态
- 显示加载动画和遮罩层
- 防止用户在加载过程中进行其他操作
- 优雅的过渡动画效果

### 4. 错误处理
- 网络请求失败时显示错误信息
- 自动重试机制（可扩展）
- 降级到传统分页方式

## 文件结构

```
YseStoreAdmin/wwwroot/businessJs/Blog/
├── blogAjaxPagination.js          # 博客页面Ajax分页脚本
├── blogCateAjaxPagination.js      # 博客分类页面Ajax分页脚本
├── ajaxPagination.css             # Ajax分页样式文件
└── README.md                      # 说明文档
```

## 后端API接口

### 博客页面
- **URL**: `/Blog?handler=AjaxPage`
- **方法**: GET
- **参数**: 
  - `PageIndex`: 页码
  - `PageSize`: 每页大小
  - `keyword`: 搜索关键词
  - `status`: 文章状态
  - `cateId`: 分类ID
  - `publishTime`: 发布时间
  - `tagId`: 标签ID

### 博客分类页面
- **URL**: `/Blog/Cate?handler=AjaxPage`
- **方法**: GET
- **参数**:
  - `PageIndex`: 页码
  - `PageSize`: 每页大小
  - `Keyword`: 搜索关键词

## 返回数据格式

```json
{
  "success": true,
  "html": "<table>...</table>",
  "pagination": {
    "currentPage": 1,
    "totalPages": 10,
    "totalCount": 100,
    "pageSize": 10
  },
  "filters": {
    "keyword": "",
    "status": "",
    "cateId": "",
    "publishTime": "",
    "tagId": ""
  }
}
```

## 使用方法

### 1. 引入文件

在页面的`@section Styles`中引入CSS：
```html
<link href="~/businessJs/Blog/ajaxPagination.css" rel="stylesheet" />
```

在页面的`@section Scripts`中引入JavaScript：
```html
<!-- 博客页面 -->
<script src="~/businessJs/Blog/blogAjaxPagination.js"></script>

<!-- 博客分类页面 -->
<script src="~/businessJs/Blog/blogCateAjaxPagination.js"></script>
```

### 2. HTML结构要求

确保页面包含以下元素：
- 表格容器：`#blog-table-container` 或 `#blog-cate-table-container`
- 分页容器：`.pager_box`
- 分页链接：`.pager a`
- 页码跳转：`#jump-page-input` 和 `#jump-page-btn`

### 3. 自动初始化

脚本会在页面加载完成后自动初始化，无需手动调用。

## 配置选项

可以通过修改JavaScript文件中的`PAGINATION_CONFIG`对象来自定义配置：

```javascript
const PAGINATION_CONFIG = {
    containerSelector: '#blog-table-container',  // 表格容器选择器
    paginationSelector: '.pager_box',            // 分页容器选择器
    ajaxUrl: '/Blog?handler=AjaxPage',           // Ajax请求URL
    loadingClass: 'ajax-loading'                 // 加载状态CSS类
};
```

## 兼容性

- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 需要支持ES6语法和Fetch API
- 自动降级到传统分页方式（如果JavaScript被禁用）

## 扩展功能

### 1. 自定义加载动画
修改`ajaxPagination.css`中的`.loading-spinner`样式

### 2. 自定义错误处理
修改JavaScript中的`showError`函数

### 3. 添加更多筛选条件
在`currentState.filters`中添加新的筛选字段

## 注意事项

1. 确保后端API返回正确的JSON格式数据
2. 页面中的原有分页事件需要被注释掉或移除，避免冲突
3. 如果页面有其他JavaScript功能，确保在Ajax更新后重新初始化
4. CSS样式可能需要根据项目的整体设计进行调整

## 故障排除

### 1. 分页不工作
- 检查JavaScript控制台是否有错误
- 确认API接口是否正常返回数据
- 检查HTML结构是否符合要求

### 2. 样式问题
- 确认CSS文件是否正确引入
- 检查是否有样式冲突
- 使用浏览器开发者工具调试

### 3. 浏览器兼容性问题
- 检查浏览器是否支持Fetch API
- 考虑添加polyfill支持旧版浏览器

## 更新日志

- **v1.0.0** (2024-08-01): 初始版本，实现基本的Ajax无感分页功能
