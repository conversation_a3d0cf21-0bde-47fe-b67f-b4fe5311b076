/**
 * Ajax分页功能样式
 * 包含加载状态、过渡动画等样式
 */

/* 加载状态样式 */
.ajax-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 加载遮罩层 */
.ajax-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

/* 加载动画 */
.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
    padding: 20px;
}

.loading-spinner::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e5e5e5;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 分页控件过渡动画 */
.pager_box {
    transition: opacity 0.3s ease;
}

.pager_box.updating {
    opacity: 0.5;
}

/* 表格内容过渡动画 */
#blog-table-container,
#blog-cate-table-container {
    transition: opacity 0.2s ease;
}

#blog-table-container.ajax-loading,
#blog-cate-table-container.ajax-loading {
    opacity: 0.6;
}

/* 分页链接悬停效果增强 */
.pager a {
    transition: all 0.2s ease;
    position: relative;
}

.pager a:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.pager a:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 123, 255, 0.1);
}

/* 当前页码样式增强 */
.pager .current {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-color: #0056b3;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
    position: relative;
}

.pager .current::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: inherit;
    z-index: -1;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.1; transform: scale(1.05); }
}

/* 页码跳转区域样式 */
.page-jump {
    display: inline-flex;
    align-items: center;
    margin-left: 20px;
    font-size: 13px;
    color: #666;
}

.page-jump input {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    width: 50px;
    text-align: center;
    margin: 0 5px;
    transition: border-color 0.2s ease;
}

.page-jump input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.page-jump button {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: 1px solid #0056b3;
    border-radius: 4px;
    padding: 4px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.page-jump button:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    border-color: #004085;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.page-jump button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 123, 255, 0.2);
}

/* 统计信息样式 */
.pager .info {
    margin-left: 20px;
    font-size: 13px;
    color: #666;
    font-weight: 500;
}

/* 省略号样式 */
.pager .ellipsis {
    color: #999;
    cursor: default;
    user-select: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .pager {
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
    }
    
    .pager a, .pager span {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .page-jump {
        margin: 10px 0 0 0;
        width: 100%;
        justify-content: center;
    }
    
    .pager .info {
        margin: 10px 0 0 0;
        width: 100%;
        text-align: center;
    }
}

/* 错误状态样式 */
.ajax-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}

.ajax-error::before {
    content: '⚠ ';
    font-weight: bold;
}

/* 成功状态样式 */
.ajax-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}

.ajax-success::before {
    content: '✓ ';
    font-weight: bold;
}

/* 表格行过渡动画 */
.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 新加载内容的淡入动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.ajax-content-loaded {
    animation: fadeIn 0.3s ease-out;
}

/* 加载骨架屏效果（可选） */
.skeleton-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 分页按钮禁用状态 */
.pager a.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* 分页容器样式优化 */
.pager_box {
    padding: 20px 0;
    border-top: 1px solid #eee;
    margin-top: 20px;
}

/* 确保分页控件在加载时不会跳动 */
.pager {
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 5px;
}
