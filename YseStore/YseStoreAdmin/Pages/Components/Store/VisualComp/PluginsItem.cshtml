@using Newtonsoft.Json.Linq
@model YseStoreAdmin.Pages.Components.Store.VisualComp.PluginsItem
@{
    var Name = Model.Name;
    var Value = Model.Value;
    var Config = Model.Config;
    var InputName = Model.InputName;

    if (Config["value"] != null && Value == null)
    {
        Value = Config["value"].ToString();
    }

    string type = Config["type"]?.ToString();
    List<string> typArray = new List<string> { "color", "font", "select", "progress", "textalign" };

    if (typArray.Contains(type) && Value == null)
    {
        Value = Config["value"].ToString();
    }
    string style = "";
    //解决关联插件无法隐藏的问题
    var visibilityAttr = "";

    if (Config["expand"] != null && Config["expand"]["visibility"] != null && Config["expand"]["visibility"].ToString() == "hidden")
    {
        style = "display: none;";
        visibilityAttr = "visibility='hidden'";
    }


    switch (type)
    {
        case "color":// 颜色组件
            {
                <div class="component_color" data-component="@type">
                    <div class="color_row">
                        <div class="color_name plugins_name">@Name</div>
                        <div class="color_box" data-colpickId="@InputName">
                            <div class="color_button"></div>
                            <input class="color_color" type="hidden" name="@InputName" value="@Value" />
                        </div>
                        <div class="clear"></div>
                    </div>
                </div>
                break;
            }

        case "font":// 字体组件
            {
                <div class="component_font" data-component="@type">
                    <div class="font_title plugins_name">>@Name</div>
                    <div class="font_box">
                        <div class="font_name" style="font-family: @(Value)">@Value</div>
                        <div class="font_btn" data-fixed-plugins="font-select">编辑</div>
                        <input type="hidden" name="@InputName" value="@Value" />
                    </div>
                </div>
                break;
            }
        case "switch":// 开关组件
            {
                string linkage = "";
                if (Config["linkage"] != null)
                {
                    linkage += " data-linkage='true'";
                }

                foreach (var item in Config["linkage"].ToObject<JObject>().Properties())
                {
                    var itemValue = item.Value.ToObject<List<string>>();
                    string itemValueStr = string.Join(',', itemValue);

                    linkage += $" data-linkage-${item.Name}='{itemValueStr}'";
                }

                if (Name == "VideoAutoPlayMute")// // 自动播放是否静音暂时隐藏功能
                {
                    Value = "0";
                    break;
                }
                if (Name == "Search" ||
                        Name == "User" ||
                        Name == "ShoppingCart" ||
                        Name == "LanguageSwitch" ||
                        Name == "CurrencySwitch" ||
                        Name == "Favorites")
                {
                    style = "display: none;";
                }

                <div class="component_switch" data-component="@type" @linkage @visibilityAttr style="@(style)">
                    <div class="switch_row">
                        <div class="switch_name plugins_name">@Name</div>
                        <div class="switch_box">
                            <div class="switchery  @( Value =="1" ? "checked" : "")">
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                        @if (Config["expand"] != null && Config["expand"]["hint"] != null)
                        {
                            <div class="switch_hint">@(GlobalLangVars.variable[Name][Config["expand"]["hint"]]) </div>
                        }

                        <input type="hidden" name="@InputName" value="@Value" />
                    </div>
                </div>

                break;
            }
        case "select":// 下拉框组件
            {
                string linkage = "";
                if (Config["linkage"] != null)
                {
                    linkage += " data-linkage='true'";
                }

                foreach (var item in Config["linkage"].ToObject<JObject>().Properties())
                {
                    var itemValue = item.Value.ToObject<List<string>>();
                    string itemValueStr = string.Join(',', itemValue);

                    linkage += $" data-linkage-${item.Name}='{itemValueStr}'";
                }

                var selectAry = new JObject();

                foreach (var kvp in Config["options"].ToObject<JObject>())
                {
                    string k = kvp.Key;
                    var v = kvp.Value.ToString();

                    selectAry[k] = new JObject
                    {
                        { "Name", GlobalLangVars.variable[Name]["options"][v] },
                        { "Value", v },
                        { "Type", "Select" }
                    };
                }

                var valueAry = new JObject
                {
                    {"Name",GlobalLangVars.variable[Name]["options"][Value]},
                    {"Value",Value},
                    {"Type","Select"}
                };
                if (Name == "ProductsMainPicScale") // 产品主图比例隐藏设置
                {
                    style = "display: none;";
                }

                if (Name == "FillingMethodPc" || Name == "FillingMethodMobile") // 轮播图隐藏填充方式和展示区域
                {
                    style = "display: none;";
                }

                if ((Name == "PosterPicHeight" || Name == "HeightPc" || Name == "HeightMobile") && (Config["expand"]["visibility"] == null || Config["expand"]["visibility"].ToString() != "show")) // 轮播图隐藏填充方式和展示区域
                {
                    style = "display: none;";
                }

                <div class="component_select" data-component="@type" @linkage @visibilityAttr style="@(style)">
                    <div class="select_name plugins_name">@GlobalLangVars.variable[Name]["name"]</div>
                    @if (Config["expand"]["hint"] != null)
                    {
                        <div class="select_hint">@GlobalLangVars.variable[Name][Config["expand"]["hint"]]</div>
                    }
                    <div class="select_box">

                        <dl class="box_drop_double" data-checkbox="0" data-showadd="0">
                            <dt class="">
                                <div class="box_select">
                                    <span>请选择</span>
                                    <input type="hidden" name="@InputName" value="@(valueAry["Select"])" class="hidden_value">
                                    <input type="hidden" name="@(InputName)Type" value="@(valueAry["Type"])" class="hidden_type">
                                </div>
                            </dt>
                            <dd class="drop_down">
                                <div class="drop_menu" data-type="">
                                    <a href="javascript:;" class="btn_back" data-value="" data-type=""
                                       data-table="" data-top="0" data-all="0" style="display:none;">返回</a>
                                    <div class="drop_skin" style="display: none;"></div>
                                    <div class="drop_list"
                                         data="@selectAry.ToJson()" data-more="none">
                                    </div>
                                    <a href="javascript:;" class="btn_load_more" data-value="" data-type="" data-table="" data-top="0"
                                       data-all="0" data-check-all="" data-start="1" style="display:none;">加载更多</a>
                                </div>
                            </dd>
                        </dl>
                    </div>
                </div>

                break;
            }
        case "input":// 单行文本组件
            {
                if (Name == "SearchPlaceholder")
                {
                    style = "margin-top: 0;";
                }

                string placeholder = "";

                <div class="component_input" data-component="@type" @visibilityAttr style="@(style)">
                    <div class="input_name plugins_name">@GlobalLangVars.variable[Name]["name"]</div>
                    <div class="input_box">
                        <input class="box_input full_input" type="text" name="@InputName" value="@Value" @placeholder />
                        @if (Config["expand"]["suffix"] != null)
                        {
                            <i>@Config["expand"]["suffix"]</i>
                        }

                    </div>
                    @if (Config["expand"]["hint"] != null)
                    {
                        <div class="input_hint">@GlobalLangVars.variable[Name][Config["expand"]["hint"]]</div>
                    }


                </div>
                break;
            }
        case "progress":// 进度条组件
            {
                int width = 0;
                if (Value.IsNullOrEmpty())
                {
                    Value = Config["value"].ToString();
                }
                var value = Config["suffix"].ToString().Replace(Value, Value);
                if (int.TryParse(value, out int val))
                {
                    if (val > 0)
                    {
                        width = (val - Config["options"][0].ObjToInt()) / (Config["options"][1].ObjToInt() - Config["options"][0].ObjToInt()) * 100;
                    }
                }

                <div class="component_progress" data-component="@type" onselectstart="return false">
                    <div class="progress_name plugins_name">
                        @GlobalLangVars.variable[Name]["name"]
                        <span>@Value</span>
                    </div>
                    <div class="progress_box">
                        <div class="progress_bar" data-min="@Config["options"][0]" data-max="@Config["options"][1]" data-suffix="@Config["suffix"]">
                            <div class="progress_belt" style="width:@width">
                                <input type="hidden" name="@InputName" value="@Value" />
                            </div>
                        </div>
                    </div>
                </div>

                break;
            }
        case "textarea":// 多行文本组件
            {

                break;
            }
        case "image":// 上传图片组件
            {
                break;
            }
        case "panel":// 面板按钮组件
            {
                break;
            }
        case "link":// 链接组件
            {
                break;
            }
        case "richtext":// 富文本组件
            {
                break;
            }
        case "textalign":// 文本对齐方式组件
            {
                break;
            }
        case "products":// 产品组件
            {
                break;
            }
        case "word":// 文字组件
            {
                break;
            }
        case "effect":// 效果组件
            {
                break;
            }
        case "category":// 分类组件
            {
                break;
            }
        case "hidden":// 隐藏组件
            {
                break;
            }
        case "time":// 时间选择组件
            {
                break;
            }
        case "nav":// 导航组件
            {
                break;
            }
        case "video":// 视频上传
            {
                break;
            }
        case "news":// 新闻组件
            {
                break;
            }
        case "coupon":// 优惠券下拉
            {
                break;
            }
        case "seo":// SEO组件
            {
                break;
            }

    }


}









