@page
@model YseStoreAdmin.Pages.Store.VisualEditModel
@{
}

@section Styles {
    <link href="/assets/css/view-v2.css" rel="stylesheet">
    <link href="/assets/js/plugin/pickr/nano.min.css" rel="stylesheet">
}
<visual-edit />
@section Scripts {

    <script src="/assets/js/plugin/pickr/pickr.es5.min.js" ></script>
    <script src="/assets/js/plugin/ckeditor/ckeditor.js" ></script>
    <script src="/assets/js/plugin/daterangepicker/moment.min.js" ></script>
    <script src="/assets/js/plugin/daterangepicker/daterangepicker.js" ></script>
    <script src="/assets/js/view-v2.js" ></script>
    <script>
                jQuery(function ($) {
        $(function(){view_obj.visual_init();})
        jQuery('#form_visual').yiiActiveForm([], []);
        });
    </script>

}