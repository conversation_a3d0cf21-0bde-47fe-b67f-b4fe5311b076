using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common.Const
{
    public class GlobalLangVars
    {

        public static readonly Dictionary<string, string> ViewTypeMenu = new Dictionary<string, string>
        {
            { "header", "页首" },
            { "banner", "主图" },
            { "footer", "页尾" },
            { "poster", "图文橱窗" },
            // "service" => "服务" is commented out, omit from C# dictionary
            // "special" => "特色优势" is commented out, omit from C# dictionary
            // "about" => "关于我们" is commented out, omit from C# dictionary
            // "review" => "点评" is commented out, omit from C# dictionary
            // "team" => "团队" is commented out, omit from C# dictionary
            { "gallery", "买家秀" },
            { "description", "产品详情" },
            { "products", "产品陈列" },
            { "blog", "动态" },
            // "product_purchase" => "产品购买" is commented out, omit from C# dictionary
            // "newsletter" => "邮件订阅" is commented out, omit from C# dictionary
            { "product_list", "产品列表" },
            { "product_description", "产品描述" },
            { "combination_purchase", "组合购买" },
            { "product_maylike", "You may like" },
            // "product_reviews" => "产品评论" is commented out, omit from C# dictionary
            // "article" => "内容" is commented out, omit from C# dictionary
            // "video" => "视频" is commented out, omit from C# dictionary
            { "customize", "自定义代码" },
            // "brands" => "品牌" is commented out, omit from C# dictionary
            { "order_tracking", "订单查询" },
            { "other", "其他" },
            { "carousel", "轮播图" },
            // "countdown" => "倒计时产品" is commented out, omit from C# dictionary
            { "blog_list", "博客列表" },
            { "blog_detail", "博客详情" },
            { "news_list", "新闻列表" },
            { "news_detail", "新闻详情" },
            { "cases_list", "案例列表" },
            { "cases_detail", "案例详情" },
            { "cases_description", "案例描述" },
            { "coupon", "优惠券" },
            { "download", "文件下载" },
            { "download_list", "下载列表" }
        };

        public static readonly Dictionary<string, string> ViewBlockName = new Dictionary<string, string>
        {
            { "Logo", "LOGO" },
            { "Poster", "图文" },
            { "Menu", "导航" },
            { "FooterMenu", "底部导航" },
            { "QuickMenu", "快速导航" },
            { "Contact", "联系方式" },
            { "Newsletter", "邮件订阅" },
            { "Video", "视频" },
            { "Products", "产品" },
            { "Social", "官方社交媒体" },
            { "CustomContent", "自定义内容" },
            { "Board", "公告栏" },
            { "ProductsCategory", "产品分类" },
            { "Team", "团队" },
            { "About", "关于我们" },
            { "Brands", "品牌" },
            { "Special", "特色优势" },
            { "Service", "服务" },
            { "Banner", "主图" },
            { "Review", "点评" },
            { "Description", "产品详细" },
            { "Carousel", "轮播图" },
            { "Countdown", "倒计时" },
            { "Coupon", "优惠券" }
        };


        public static readonly JObject variable = new JObject();

    }
}
