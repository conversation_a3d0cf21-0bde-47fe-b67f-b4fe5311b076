using Aop.Api.Domain;
using Entitys;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Org.BouncyCastle.Utilities;
using SqlSugar;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.IService;
using YseStore.IService.HelpsOrder;
using YseStore.IService.Order;
using YseStore.IService.Pay;
using YseStore.Model;
using YseStore.Model.Entities.Blog;
using YseStore.Model.VM.Payment.Paypal;
using YseStore.Service.HelpsOrder;

namespace YseStore.Service.Order
{
    public class OrderConsumerService : IOrderConsumerService
    {
        private readonly ISqlSugarClient db;
        public ICurrencyService _currencyService;
        private readonly IServiceProvider _serviceProvider;
        public OrderConsumerService(ISqlSugarClient db, ICurrencyService currencyService, IServiceProvider serviceProvider)
        {
            this.db = db;
            _currencyService = currencyService;
            _serviceProvider = serviceProvider;
        }
        /// <summary>
        /// 前端我的订单列表
        /// </summary>
        /// <param name="keywords"></param>
        /// <param name="OrderStatus"></param>
        /// <param name="pageNum"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PagedList<OrderResponse>> QueryAsync(int UserId = 0, string keywords = "", int OrderStatus = 0,
           int pageNum = 1, int pageSize = 20)
        {
            try
            {
                var query = db.Queryable<orders>()
              .LeftJoin<orders_products_list>((o, opl) => o.OrderId == opl.OrderId)
              .LeftJoin<orders_package>((o, opl, op) => o.OrderId == op.OrderId)
              .Where((o, opl, op) => o.UserId == UserId)
             .WhereIF(!string.IsNullOrWhiteSpace(keywords), (o, opl, op) => o.OId.Contains(keywords) || opl.SKU.Contains(keywords) || opl.Name.Contains(keywords)
             || op.TrackingNumber.Contains(keywords))
             .WhereIF(OrderStatus != 0, o => o.OrderStatus == OrderStatus)
          .Select(o => o);

                var orderList = query.Distinct().OrderByDescending(o => o.OrderId).ToList();

                // 执行分页查询
                var paged = orderList.Skip((pageNum - 1) * pageSize).Take(pageSize).ToList();
                //var res = new PagedList<user>(results, pageNum - 1, pageSize, results.Count);

                var grouped = paged
                    .Select(o => (OrderResponse)o)
                    .OrderByDescending(o => o.OrderId)
                    .ToList();
                //var userIds = grouped.Select(p => p.UserId).ToList();
                //var orderIds = grouped.Select(p => p.OrderId).ToList();
                //var userList = db.Queryable<user>()
                //     .Where(p => userIds.Contains(p.UserId))
                //   .ToList();
                //var ordersProductsList = db.Queryable<orders_products_list>()
                //     .Where(p => orderIds.Contains(Convert.ToInt32(p.OrderId)))
                //   .ToList();

                var countryList = db.Queryable<country>()
                   .ToList();
                //var currencyList = db.Queryable<currency>().ToList();
                var currencyList = await _currencyService.GetAllCurrencyCache();
                using var container = _serviceProvider.CreateScope();
                var _orderListService = container.ServiceProvider.GetService<IOrderListService>();
                foreach (var p in grouped)
                {
                    var price = await _orderListService.CalculateActualPayment(p.OrderId, 0);
                    //decimal OrderSum = Math.Round(((p.ProductPrice + p.ShippingPrice - p.PointsPrice) + ((p.ProductPrice + p.ShippingPrice - p.PointsPrice) * p.PayAdditionalFee)).Value, 2, MidpointRounding.AwayFromZero);
                    p.OrderSum = price;
                    p.OrderSymbol = currencyList.FirstOrDefault(c => c.Currency == p.Currency)?.Symbol;
                    p.countrys = countryList.Where(x => x.Country == p.ShippingCountry).FirstOrDefault();
                    p.OIdToBase64 = p.OId.ToBase64();
                    p.OrderTimeStr = DateTimeHelper.ConvertToBeijingTime(Convert.ToInt32(p.OrderTime)).ToString("MMM d, yyyy");
                }
               
                
                var resss = new PagedList<OrderResponse>(grouped, pageNum - 1, pageSize, orderList.Count);
                return resss;
            }
            catch (Exception ex)
            {

                return new PagedList<OrderResponse>(new List<OrderResponse>(), pageNum, pageSize, 0);
            }
        }

        /// <summary>
        /// 根据订单号获取订单详情
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<orders> GetOrderDetailByOrderIdAsync(int OrderId, int UserId = 0)
        {
            var orders = await db.Queryable<orders>()
                  .Where(o => o.OrderId == OrderId && o.UserId == UserId)
                  .FirstAsync();
            return orders;
        }
        /// <summary>
        /// 根据订单号获取订单日志
        /// </summary>
        /// <param name="OrderId"></param>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public async Task<List<orders_log>> GetOrdersLogByOrderIdAsync(int OrderId, int UserId = 0)
        {
            var orders = await db.Queryable<orders_log>()
                  .Where(o => o.OrderId == OrderId && o.UserId == UserId)
                  .ToListAsync();
            return orders;
        }


        /// <summary>
        /// 根据userid获取用户详情
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<user> GetUserByUserIdAsync(int UserId = 0)
        {
            var ret = await db.Queryable<user>()
                  .Where(o => o.UserId == UserId)
                  .FirstAsync();
            return ret;
        }
        /// <summary>
        /// 修改用户信息
        /// </summary>
        /// <param name="userdata"></param>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public async Task<bool> UpdateUserDataByUserId(user userdata,int UserId = 0)
        {
            try
            {
               var b=  db.Updateable(userdata)
                    .Where(o => o.UserId == UserId)
                    .ExecuteCommandHasChange();
                return b;
            }
            catch (Exception ex)
            {
                return false;
                throw;
            }

        }



        /// <summary>
        /// 根据订单id返回产品总价相关数据
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<(int, decimal, decimal, decimal, decimal, decimal, string, string, decimal, decimal, decimal)> GetOrdersAmount(int orderid, int UserId = 0)
        {
            var OrdersModel = await db.Queryable<orders>()
                 .Where(x => x.OrderId == orderid && x.UserId == UserId)
                 .FirstAsync();
            var currencyRow = await _currencyService.GetAllCurrencyCache();
            var currencyList = currencyRow.Where(c => c.Currency == OrdersModel.Currency).First();

            using var container = _serviceProvider.CreateScope();
            var _orderListService = container.ServiceProvider.GetService<IOrderListService>();
            var priceData = await _orderListService.OrdersDetailPrice(OrdersModel, 1, 0, false, true);



            var ordersProductsList = db.Queryable<orders_products_list>().Where(opl => opl.OrderId == OrdersModel.OrderId).ToList();

            int ProductCount = ordersProductsList.Count;
            //decimal TotalProductPrice = Math.Round((OrdersModel.ProductPrice).Value, 2, MidpointRounding.AwayFromZero);
            //decimal points = Math.Round(OrdersModel.PointsPrice, 2, MidpointRounding.AwayFromZero);
            //decimal shippingFee = Math.Round((OrdersModel.ShippingPrice).Value, 2, MidpointRounding.AwayFromZero);
            //var OrderPartSum = OrdersModel.ProductPrice + OrdersModel.ShippingPrice - OrdersModel.PointsPrice;
            //decimal commission = Math.Round(((OrderPartSum * OrdersModel.PayAdditionalFee)).Value, 2, MidpointRounding.AwayFromZero);
            //decimal OrderSum = Math.Round((OrderPartSum + (OrderPartSum * OrdersModel.PayAdditionalFee)).Value, 2, MidpointRounding.AwayFromZero);
            string OrderSymbol = currencyList.Symbol;
            string paymentMethod = OrdersModel.PaymentMethod;
            //return (ProductCount, TotalProductPrice, points, shippingFee, commission, OrderSum, OrderSymbol, paymentMethod);

            return (ProductCount, priceData.ProductPrice, priceData.PointsPrice, priceData.ShippingPrice, priceData.FeePrice,
           priceData.TotalPrice, OrderSymbol, paymentMethod, priceData.DiscountPrice, priceData.CouponPrice, priceData.TaxPrice);

        }
        /// <summary>
        /// 根据订单id返回退款信息
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<orders_refund_info> GetOrderRefundByOrderIdAsync(int OrderId)
        {
            var orders = await db.Queryable<orders_refund_info>()
                  .Where(o => o.OrderId == OrderId && o.Status == "success")
                  .FirstAsync();
            return orders;
        }
        /// <summary>
        /// 根据orderid获取包裹信息
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<List<orders_package>> GetPackageAsync(int orderid)
        {
            var res = await db.Queryable<orders_package>()
                .Where(op => op.OrderId == orderid)
                .ToListAsync();
            return res;
        }


        /// <summary>
        /// 根据订单id返回订单产品列表
        /// </summary>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        public async Task<List<ordersProductsListResponse>> GetOrders_Products_Lists(int OrderId)
        {
            var ordersFirst = db.Queryable<orders>()
                .Where(x => x.OrderId == OrderId)
                .First();
            var ordersProductsLists = db.Queryable<orders_products_list>()
                .Where(o => o.OrderId == OrderId)
                .ToList();
            var grouped = ordersProductsLists
                    .Select(o => (ordersProductsListResponse)o)
                    .OrderByDescending(o => o.LId)
                    .ToList();
            var ProIds = grouped.Select(o => o.ProId).ToList();
            var productsList = db.Queryable<products>()
                .Where(x => ProIds.Contains(x.ProId))
                .ToList();
            using var container = _serviceProvider.CreateScope();
            var _helpsCartService = container.ServiceProvider.GetService<IHelpsCartService>();
            if (grouped!=null&& grouped.Count>0)
            {
                foreach (var p in grouped)
                {
                    var productsData = productsList.Where(x => x.ProId == p.ProId).FirstOrDefault();
                    p.PageUrl = productsData?.PageUrl??"";
                    p.ProductPriceStr = await _helpsCartService.IconvPriceFormat(p.ProductPrice, 2, ordersFirst.Currency, ordersFirst.Rate);
                    p.PriceStr = await _helpsCartService.IconvPriceFormat(p.Price.Value, 2, ordersFirst.Currency, ordersFirst.Rate);

                }
            }
            //if (ordersProductsLists != null && ordersProductsLists.Count == 0)
            //{
            //    foreach (var item in ordersProductsLists)
            //    {
            //        if (!string.IsNullOrEmpty(item.Property) && item.Property != "[]")
            //        {
            //            var dictProperty = JsonConvert.DeserializeObject<Dictionary<string, string>>(item.Property);
            //            foreach (var items in dictProperty)
            //            {
            //                item.Property += items.Key + ":" + items.Value;
            //            }

            //        }

            //    }
            //}
            return grouped;
        }
        /// <summary>
        /// 根据userid获取订单日志列表
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public async Task<MessageOrdersResponse> GetordersLogListByUserId(int UserId)
        {
            var UserIFirst = db.Queryable<user>()
                .Where(x => x.UserId == UserId).First();

            var mesList = db.Queryable<user_message>()
                  .Where(x => x.Module == "others" && x.UserId == UserId)
                  .OrderBy(x => x.MId, OrderByType.Asc)
                  .ToList();
            //db.Updateable(mesList)
            //  .ReSetValue(it => { it.IsRead = true; })
            //  .ExecuteCommand();
            await db.Updateable<user_message>()
                     .SetColumns(it => it.IsRead == true)
                     .Where(it => it.UserId == UserId)
                     .Where(it => it.UserType == "manager")
                     .ExecuteCommandHasChangeAsync();
            var grouped = mesList
                   .Select(x => (UserMessageMessageOrdersResponse)x)
                   .ToList();
            var res = new MessageOrdersResponse()
            {
                OId = UserId.ToString(),
                MId = mesList.FirstOrDefault()?.MId.ToString(),
                Email = UserIFirst.Email,
                Reply = grouped
            };
            return res;
        }

        /// <summary>
        /// 根据订单号获取订单日志列表
        /// </summary>
        /// <param name="OId"></param>
        /// <returns></returns>
        public async Task<MessageOrdersResponse> GetordersLogListByOId(string OId)
        {
            var orderFirst = db.Queryable<orders>()
                .Where(x => x.OId == OId)
                .First();
            var userMessageFirst = db.Queryable<user_message>()
                .Where(x => x.Module == "orders" && x.Subject == OId)
                .First();
            if (userMessageFirst == null)
            {
                userMessageFirst = new user_message();
                userMessageFirst.UserId = Convert.ToInt32(orderFirst.UserId);
                userMessageFirst.Module = "orders";
                userMessageFirst.Type = false;
                userMessageFirst.CusEmail = "";
                userMessageFirst.Subject = OId;
                userMessageFirst.Content = "No#" + OId;
                userMessageFirst.PicPath = "";
                userMessageFirst.IsRead = true;
                userMessageFirst.IsReply = false;
                userMessageFirst.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                userMessageFirst.ParentId = 0;
                userMessageFirst.EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                userMessageFirst.UserType = "manager";
                userMessageFirst.VideoPath = "";
                db.Insertable(userMessageFirst).ExecuteReturnIdentity();
            }

            var mesList = db.Queryable<user_message>()
                  .Where(x => x.Module == "orders" && x.Subject == OId)
                  .OrderBy(x => x.MId, OrderByType.Asc)
                  .ToList();
            db.Updateable(mesList)
              .ReSetValue(it => { it.IsRead = true; })
              .ExecuteCommand();
            var grouped = mesList
                   .Select(x => (UserMessageMessageOrdersResponse)x)
                   .ToList();
            var res = new MessageOrdersResponse()
            {
                OId = OId,
                MId = mesList.FirstOrDefault()?.MId.ToString(),
                Email = orderFirst.Email,
                Reply = grouped
            };
            return res;
        }
        /// <summary>
        /// 订单消息-管理员回复
        /// </summary>
        /// <param name="Message"></param>
        /// <param name="MsgPicPath"></param>
        /// <param name="MsgVideoPath"></param>
        /// <param name="MId"></param>
        /// <returns></returns>
        public async Task<UserMessageMessageOrdersResponse> AddordersLogList(string Message, string MsgPicPath, string MsgVideoPath, int MId)
        {
            var userMessageFirst = db.Queryable<user_message>()
                .Where(x => x.Module == "orders" && x.MId == MId)
                .First();
            var orderFirst = db.Queryable<orders>()
                .Where(x => x.OId == userMessageFirst.Subject)
                .First();
            var id = 0;
            if (userMessageFirst != null)
            {
                userMessageFirst = new user_message();
                userMessageFirst.UserId = Convert.ToInt32(orderFirst.UserId);
                userMessageFirst.Module = "orders";
                userMessageFirst.Type = false;
                userMessageFirst.CusEmail = "";
                userMessageFirst.Subject = orderFirst.OId;
                userMessageFirst.Content = Message;
                userMessageFirst.PicPath = MsgPicPath;
                userMessageFirst.IsRead = false;
                userMessageFirst.IsReply = false;
                userMessageFirst.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                userMessageFirst.ParentId = MId;
                userMessageFirst.EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                userMessageFirst.UserType = "user";
                userMessageFirst.VideoPath = MsgVideoPath;
                id = db.Insertable(userMessageFirst).ExecuteReturnIdentity();
            }

            //Message "111"
            //MsgPicPath  "https://static.retekess.com/u_file/20250609/20250609142423_1493.jpg"
            //MsgVideoPath    ""
            //fileCover   "/manage/web/shop/images/set/default_cover.jpg"
            //MId "57"

            //          {
            //              "UserId": 35,
            //"Module": "orders",
            //"UserType": "manager",
            //"Subject": "4410844",
            //"Content": "2222",
            //"PicPath": "",
            //"IsRead": 0,
            //"AccTime": 1749465537,
            //"EditTime": 1749465537,
            //"ParentId": 60,
            //"VideoPath": "",
            //"Time": "2025-06-09 18:38:57",
            //"VideoPathCover": ""

            //  }

            if (id > 0)
            {
                var mesList = db.Queryable<user_message>()
                  .Where(x => x.Module == "orders" && x.MId == id)
                  .OrderBy(x => x.MId, OrderByType.Desc)
                  .ToList();
                var res = mesList
                       .Select(x => (UserMessageMessageOrdersResponse)x)
                       .First();

                return res;
            }
            else
            {
                return new UserMessageMessageOrdersResponse();
            }

        }
        /// <summary>
        /// 站内信-管理员回复
        /// </summary>
        /// <param name="Message"></param>
        /// <param name="MsgPicPath"></param>
        /// <param name="MsgVideoPath"></param>
        /// <param name="MId"></param>
        /// <returns></returns>
        public async Task<UserMessageMessageOrdersResponse> AddMyInboxLogList(string Message, string MsgPicPath, string MsgVideoPath, int MId, int userId)
        {
            //if (MId > 0)
            //{

            //}
            //var userMessageFirst = db.Queryable<user_message>()
            //    .Where(x => x.Module == "others" && x.MId == MId)
            //    .First();
            //var id = 0;

            var userMessageFirst = new user_message();
            userMessageFirst.UserId = userId;
            userMessageFirst.Module = "others";
            userMessageFirst.Type = false;
            userMessageFirst.CusEmail = "";
            userMessageFirst.Subject = "";
            userMessageFirst.Content = Message;
            userMessageFirst.PicPath = MsgPicPath;
            userMessageFirst.IsRead = false;
            userMessageFirst.IsReply = false;
            userMessageFirst.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
            userMessageFirst.ParentId = MId;
            userMessageFirst.EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
            userMessageFirst.UserType = "user";
            userMessageFirst.VideoPath = MsgVideoPath;
            var id = db.Insertable(userMessageFirst).ExecuteReturnIdentity();

            if (id > 0)
            {
                var mesList = db.Queryable<user_message>()
                  .Where(x => x.Module == "others" && x.MId == id)
                  .OrderBy(x => x.MId, OrderByType.Desc)
                  .ToList();
                var res = mesList
                       .Select(x => (UserMessageMessageOrdersResponse)x)
                       .First();

                return res;
            }
            else
            {
                return new UserMessageMessageOrdersResponse();
            }

        }

        /// <summary>
        /// 确认收货
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<bool> OrdersReceivingAction(int id)
        {
            try
            {
                // 验证订单
                var orderRow = db.Queryable<orders>()
                    .Where(o => o.OrderId == id)
                    .First();

                // 普通取消逻辑
                db.Updateable<orders>()
                    .SetColumns(o => o.OrderStatus == 6)
                    .SetColumns(o => o.UpdateTime == DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now))
                    .Where(o => o.OrderId == id)
                .ExecuteCommand();
                using var container = _serviceProvider.CreateScope();
                var _helpsWriteOrderLogService = container.ServiceProvider.GetService<IHelpsWriteOrderLogService>();
                var logOption = await _helpsWriteOrderLogService.GetWriteOrderLogData("completed");
                // 记录取消日志
                WriteOrderLog(0, Convert.ToInt32(orderRow.UserId), orderRow.ShippingFirstName+orderRow.ShippingLastName, id, 0,
                    logOption.LogMessage, logOption.LogTitle, logOption.LogData, 0);
                // 取消订单后 检测并是否返还优惠券
                //RefundCouponAction(id);
                return true;
            }
            catch (Exception ex)
            {
                return false;
                throw;
            }

        }
        /// <summary>
        /// 取消订单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<bool> OrdersCancelAction(int id)
        {
            try
            {
                // 验证订单
                var orderRow = db.Queryable<orders>()
                    .Where(o => o.OrderId == id)
                    .First();

                // 普通取消逻辑
                db.Updateable<orders>()
                    .SetColumns(o => o.CancelReason == "取消订单")
                    .SetColumns(o => o.OrderStatus == 7)
                    .SetColumns(o => o.PaymentStatus == "voided")
                    .Where(o => o.OrderId == id)
                .ExecuteCommand();

                // 记录取消日志
                WriteOrderLog(1, Convert.ToInt32(orderRow.UserId), "", id, 0,
                    "voided", "Order Voided", "取消订单", 0);
                // 取消订单后 检测并是否返还优惠券
                RefundCouponAction(id);
                return true;
            }
            catch (Exception ex)
            {
                return false;
                throw;
            }

        }

        /// <summary>
        /// 取消订单时处理优惠券返还
        /// </summary>
        public async void RefundCouponAction(int orderId = 0)
        {
            var ordersRow = db.Queryable<orders>()
                .Where(x => x.OrderId == orderId)
                .First();

            if (ordersRow == null || string.IsNullOrEmpty(ordersRow.CouponCode)) return;

            var couponRow = await db.Queryable<sales_coupon>()
                .FirstAsync(c => c.CouponNumber == ordersRow.CouponCode);

            if (couponRow == null ||
                couponRow.OrderCancelReturn != true ||
                couponRow.CId == 0 ||
                ordersRow.UserId == 0)
            {
                return;
            }

            var couponLog = await db.Queryable<sales_coupon_log>()
                .FirstAsync(l => l.CId == couponRow.CId &&
                               l.UserId == ordersRow.UserId &&
                               l.OrderId == ordersRow.OrderId);

            if (couponLog != null)
            {
                couponLog.Status = "returned";
                couponLog.ReturnTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);

                db.Updateable(couponLog)
                    .UpdateColumns(it => new
                    {
                        it.Status,
                        it.ReturnTime
                    })
                    .ExecuteCommand();
                UpdateCouponStatus(couponRow, ordersRow.OrderId, 0);
            }
        }

        /// <summary>
        /// 更新优惠券过期状态
        /// </summary>
        /// <param name="couponRow">优惠券记录</param>
        /// <param name="orderId">订单ID</param>
        /// <param name="tempOrderId">临时订单ID</param>
        public void UpdateCouponStatus(dynamic couponRow, int orderId = 0, int tempOrderId = 0)
        {
            DateTime currentTime = DateTime.Now;
            int couponId = couponRow?.CId ?? 0;
            string email = "";
            int userId = 0;

            // 获取用户信息
            if (orderId != 0)
            {
                var order = db.Queryable<orders>()
                    .Where(x => x.OrderId == orderId)
                    .First();
                if (order != null)
                {
                    userId = Convert.ToInt32(order.UserId);
                    email = order.Email;
                }
            }
            else if (tempOrderId != 0)
            {
                var tempOrder = db.Queryable<temp_orders>().InSingle(tempOrderId);
                if (tempOrder != null)
                {
                    email = tempOrder.Email;
                }
            }

            // 构建查询条件
            //var where = PredicateBuilder.True<SalesCouponRelation>();
            //if (userId != 0)
            //{
            //    where = where.And(c => c.CId == couponId && c.UserId == userId);
            //}
            //else if (!string.IsNullOrEmpty(email))
            //{
            //    where = where.And(c => c.CId == couponId && c.Email == email);
            //}

            if (userId != 0 || !string.IsNullOrEmpty(email))
            {
                var couponRelation = db.Queryable<sales_coupon_relation>()
                    .WhereIF(userId != 0, c => c.CId == couponId && c.UserId == userId)
                    .WhereIF(!string.IsNullOrEmpty(email), c => c.CId == couponId && c.Email == email)
                                     .First();

                if (couponRelation != null)
                {
                    int status = 1;
                    DateTime start = DateTimeHelper.ConvertToBeijingTime(couponRelation.StartTime);
                    DateTime end = DateTimeHelper.ConvertToBeijingTime(couponRelation.EndTime);

                    // 检查时间有效性
                    if (currentTime < start) status = -1;    // 未开始
                    else if (currentTime > end) status = -2;  // 已过期
                    else
                    {
                        // 检查使用次数
                        int usedCount = db.Queryable<sales_coupon_log>()
                                        .Where(l => l.CId == couponId &&
                                                   l.Email == email &&
                                                   l.Status == "used")
                                        .Count();

                        if (couponRelation.UnLmUseNum == false &&
                            usedCount >= couponRelation.UseNum)
                        {
                            status = -3; // 次数用完
                        }
                    }

                    // 更新过期状态
                    db.Updateable<sales_coupon_relation>()
                      .SetColumns(c => new sales_coupon_relation
                      {
                          IsExpired = (SByte)(status < 0 ? 1 : 0)
                      })
                       .WhereIF(userId != 0, c => c.CId == couponId && c.UserId == userId)
                    .WhereIF(!string.IsNullOrEmpty(email), c => c.CId == couponId && c.Email == email)
                      .ExecuteCommand();
                }
            }
        }

        /// <summary>
        /// 生成订单日志
        /// </summary>
        /// <param name="isAdmin">是否为管理员 0:买家 1:管理员 2:API 3:APP</param>
        /// <param name="userId">会员ID或管理员ID</param>
        /// <param name="userName">会员名字或管理员名字</param>
        /// <param name="orderId">订单ID</param>
        /// <param name="isTemp">是否为临时订单</param>
        /// <param name="log">记录日志标题</param>
        /// <param name="logTitle">日志管理标题</param>
        /// <param name="logData">记录日志数据</param>
        /// <param name="tId">支付记录关联ID</param>
        public void WriteOrderLog(int isAdmin, int userId, string userName, int orderId,
            int isTemp = 0, string log = "", string logTitle = "", string logData = "", int tId = 0)
        {
            string prefix = isTemp == 1 ? "Temp" : "";
            var orderRow = db.Queryable<orders>()
                            .Where(o => o.OrderId == orderId)
                            .First(); // 对应Yii的limit(1)->one()
            var manageSession = db.Queryable<Entitys.manage>()
                .Where(x => x.UserName != "u")
                .First();

            // 处理不同操作来源
            switch (isAdmin)
            {
                case 1 when manageSession != null:

                    if (manageSession != null)
                    {
                        userId = manageSession?.UserId ?? 0;
                        userName = manageSession?.UserName?.ToString() ?? "";
                    }
                    break;
                case 2:
                    userId = 0;
                    userName = "API";
                    break;
                case 3:
                    userId = 0;
                    userName = "APP";
                    break;
            }

            // 创建日志实体
            var ordersLog = new orders_log
            {
                UserId = userId,
                IsAdmin = Convert.ToBoolean(isAdmin),
                UserName = userName,
                OrderId = orderId,
                TempOrderId = (int)(isTemp == 1 ? orderId : 0), // 处理前缀字段
                PaymentStatus = orderRow?.PaymentStatus,
                ShippingStatus = orderRow?.ShippingStatus,
                OrderStatus = orderRow?.OrderStatus,
                Ip = "", // 需要实现IP获取方法
                Log = log,
                LogManage = logTitle,
                LogData = logData,
                TId = tId,
                AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now) // 对应$c['time']
            };

            // 插入日志
            db.Insertable(ordersLog).ExecuteCommand();

            // 处理支付时间更新
            if (isTemp == 0 && orderRow != null &&
                (orderRow.PaymentStatus == "paid" || orderRow.PaymentStatus == "pending") &&
                orderRow.PayTime == null)
            {
                db.Updateable<orders>()
                    .SetColumns(o => new orders { PayTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now) })
                    .Where(o => o.OrderId == orderId)
                    .ExecuteCommand();
            }
        }








    }
}
