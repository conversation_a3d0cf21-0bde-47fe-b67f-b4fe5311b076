using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.Model.Entities.Blog;
using YseStore.IService.Blog;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using YseStore.IService;
using YseStore.Model;

namespace Blog.Areas.Blog.Pages
{
    public class BlogCateModel : PageModel
    {
        private readonly IBlogCategoryService _blogCategoryService;
        private readonly IViewRenderService _viewRenderService;

        public BlogCateModel(IBlogCategoryService blogCategoryService, IViewRenderService viewRenderService)
        {
            _blogCategoryService = blogCategoryService;
            _viewRenderService = viewRenderService;
        }

        /// <summary>
        /// 博客分类列表
        /// </summary>
        public PageModel<BlogNewCategory> Categories { get; set; }

        /// <summary>
        /// 分类博客数量字典
        /// </summary>
        public Dictionary<short, int> BlogCounts { get; set; }

        /// <summary>
        /// 搜索关键词
        /// </summary>
        [BindProperty(SupportsGet = true)]
        public string Keyword { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        [BindProperty(SupportsGet = true)]
        public int PageIndex { get; set; } = 1;
        
        /// <summary>
        /// 当前页码（用于分页显示）
        /// </summary>
        [BindProperty(SupportsGet = true)]
        public int CurrentPage 
        { 
            get => PageIndex;
            set => PageIndex = value;
        }

        /// <summary>
        /// 每页记录数
        /// </summary>
        public int PageSize { get; set; } = 10;
        
        /// <summary>
        /// 状态消息
        /// </summary>
        [TempData]
        public string StatusMessage { get; set; }
        
        /// <summary>
        /// 删除分类ID
        /// </summary>
        [BindProperty]
        public short id { get; set; }

        /// <summary>
        /// 用于接收排序数据的模型
        /// </summary>
        public class OrderItem
        {
            public short categoryId { get; set; }
            public int targetOrder { get; set; }
        }

        public async Task OnGetAsync()
        {
            // 确保PageIndex为当前页码
            if (CurrentPage > 0)
            {
                PageIndex = CurrentPage;
            }

            try
            {
                // 获取博客分类列表 - 确保传递正确的PageSize
                Categories = await _blogCategoryService.GetCategoryList(Keyword, PageIndex, PageSize);

                // 确保PageSize在返回的模型中也是正确的
                if (Categories != null)
                {
                    Categories.PageSize = PageSize;
                }

                // 如果有分类数据，获取每个分类的博客数量
                if (Categories != null && Categories.data != null && Categories.data.Any())
                {
                    var categoryIds = Categories.data.Select(c => c.CateId).ToList();
                    BlogCounts = await _blogCategoryService.GetBlogCountByCategoryIds(categoryIds);
                }
                else
                {
                    BlogCounts = new Dictionary<short, int>();
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息
                Console.WriteLine($"获取博客分类列表时出错: {ex.Message}");
                Categories = new PageModel<BlogNewCategory>
                {
                    page = PageIndex,
                    PageSize = PageSize,
                    dataCount = 0,
                    data = new List<BlogNewCategory>()
                };
                BlogCounts = new Dictionary<short, int>();
            }
        }

        /// <summary>
        /// 获取特定分类的博客数量
        /// </summary>
        public int GetBlogCount(short categoryId)
        {
            if (BlogCounts != null && BlogCounts.ContainsKey(categoryId))
            {
                return BlogCounts[categoryId];
            }
            return 0;
        }
        
        /// <summary>
        /// 单个分类删除处理器 - 直接由系统API调用
        /// </summary>
        public async Task<IActionResult> OnGetDeleteCategoryAsync(short id)
        {
            try
            {
                if (id <= 0)
                {
                    StatusMessage = "错误：无效的分类ID";
                    return RedirectToPage("./Cate", new { PageIndex, Keyword });
                }
                
                bool success = await _blogCategoryService.DeleteCategory(id);
                
                if (success)
                {
                    StatusMessage = "成功：分类已删除";
                }
                else
                {
                    StatusMessage = "错误：删除分类失败";
                }
                
                // 重定向回列表页，保留当前搜索和分页参数
                return RedirectToPage("./Cate", new { PageIndex, Keyword });
            }
            catch (Exception ex)
            {
                StatusMessage = $"错误：删除失败: {ex.Message}";
                return RedirectToPage("./Cate", new { PageIndex, Keyword });
            }
        }

        /// <summary>
        /// 批量删除处理器 - 直接由系统API调用
        /// </summary>
        public async Task<IActionResult> OnGetDeleteBatchAsync(string ids)
        {
            try
            {
                if (string.IsNullOrEmpty(ids))
                {
                    StatusMessage = "警告：请至少选择一个分类";
                    return RedirectToPage("./Cate", new { PageIndex, Keyword });
                }
                
                // 解析ID字符串
                var idList = ids.Split(',').Select(short.Parse).ToList();
                
                bool success = true;
                foreach (var id in idList)
                {
                    success &= await _blogCategoryService.DeleteCategory(id);
                }
                
                if (success)
                {
                    StatusMessage = "成功：选中的分类已删除";
                }
                else
                {
                    StatusMessage = "警告：部分删除操作失败";
                }
                
                // 重定向回列表页，保留当前搜索和分页参数
                return RedirectToPage("./Cate", new { PageIndex, Keyword });
            }
            catch (Exception ex)
            {
                StatusMessage = $"错误：批量删除失败: {ex.Message}";
                return RedirectToPage("./Cate", new { PageIndex, Keyword });
            }
        }

        /// <summary>
        /// 更新分类排序处理器 - 处理拖拽排序后的顺序更新
        /// </summary>
        public async Task<IActionResult> OnPostUpdateOrderAsync([FromForm] string orderData)
        {
            try
            {
                if (string.IsNullOrEmpty(orderData))
                {
                    return new JsonResult(new { success = false, message = "未接收到排序数据" });
                }

                // 解析排序数据
                var orderItems = JsonConvert.DeserializeObject<List<OrderItem>>(orderData);
                if (orderItems == null || !orderItems.Any())
                {
                    return new JsonResult(new { success = false, message = "排序数据格式无效" });
                }

                // 去除重复的分类ID，保留最后一个排序值
                var distinctOrderItems = orderItems
                    .GroupBy(x => x.categoryId)
                    .Select(g => g.Last())
                    .ToList();

                // 调用服务更新排序
                bool success = await _blogCategoryService.UpdateCategoriesOrder(distinctOrderItems.ToDictionary(x => x.categoryId, x => x.targetOrder));

                if (success)
                {
                    return new JsonResult(new { success = true });
                }
                else
                {
                    return new JsonResult(new { success = false, message = "更新排序失败" });
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"更新排序时发生错误: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取表格内容
        /// </summary>
        public async Task<IActionResult> OnGetRefreshTableAsync()
        {
            try
            {
                // 确保PageIndex为当前页码
                if (CurrentPage > 0)
                {
                    PageIndex = CurrentPage;
                }

                // 获取博客分类列表 - 确保传递正确的PageSize
                Categories = await _blogCategoryService.GetCategoryList(Keyword, PageIndex, PageSize);

                // 确保PageSize在返回的模型中也是正确的
                if (Categories != null)
                {
                    Categories.PageSize = PageSize;
                }

                // 如果有分类数据，获取每个分类的博客数量
                if (Categories != null && Categories.data != null && Categories.data.Any())
                {
                    var categoryIds = Categories.data.Select(c => c.CateId).ToList();
                    BlogCounts = await _blogCategoryService.GetBlogCountByCategoryIds(categoryIds);
                }
                else
                {
                    BlogCounts = new Dictionary<short, int>();
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息
                Console.WriteLine($"刷新表格时出错: {ex.Message}");
                Categories = new PageModel<BlogNewCategory>
                {
                    page = PageIndex,
                    PageSize = PageSize,
                    dataCount = 0,
                    data = new List<BlogNewCategory>()
                };
                BlogCounts = new Dictionary<short, int>();
            }

            // 返回表格内容
            return Partial("_BlogCateTablePartial", this);
        }

        /// <summary>
        /// Ajax分页接口 - 返回分页信息
        /// </summary>
        /// <returns>包含分页信息的JSON</returns>
        public async Task<IActionResult> OnGetAjaxPageAsync()
        {
            try
            {
                // 确保PageIndex为当前页码
                if (CurrentPage > 0)
                {
                    PageIndex = CurrentPage;
                }

                // 获取博客分类列表
                Categories = await _blogCategoryService.GetCategoryList(Keyword, PageIndex, PageSize);

                // 确保PageSize在返回的模型中也是正确的
                if (Categories != null)
                {
                    Categories.PageSize = PageSize;
                }

                // 如果有分类数据，获取每个分类的博客数量
                if (Categories != null && Categories.data != null && Categories.data.Any())
                {
                    var categoryIds = Categories.data.Select(c => c.CateId).ToList();
                    BlogCounts = await _blogCategoryService.GetBlogCountByCategoryIds(categoryIds);
                }
                else
                {
                    BlogCounts = new Dictionary<short, int>();
                }

                // 计算分页信息
                var totalCount = Categories?.dataCount ?? 0;
                var totalPages = Categories?.pageCount ?? 0;
                var currentPage = PageIndex > 0 ? PageIndex : 1;

                // 返回JSON数据
                return new JsonResult(new
                {
                    success = true,
                    needRefresh = true,
                    pagination = new
                    {
                        currentPage = currentPage,
                        totalPages = totalPages,
                        totalCount = totalCount,
                        pageSize = PageSize
                    },
                    filters = new
                    {
                        keyword = Keyword ?? ""
                    }
                });
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    success = false,
                    message = "获取数据失败：" + ex.Message
                });
            }
        }
    }
}
