@page "{handler?}"
@model Blog.Areas.Blog.Pages.BlogCateModel
@{
    var totalCount = Model.Categories?.dataCount ?? 0;
    var pageSize = Model.PageSize;
    // 确保totalPages正确计算，考虑PageSize
    var totalPages = totalCount > 0 ? (int)Math.Ceiling((decimal)totalCount / pageSize) : 0;

    // 调试信息
    // var debugInfo = $"总记录数: {totalCount}, 每页大小: {pageSize}, 总页数: {totalPages}";
}

@section Styles {
    <link href="~/businessJs/Blog/ajaxPagination.css" rel="stylesheet" />
    <style>
        /* 分页样式 */
        .pager_box {
            margin: 20px 0;
            text-align: center;
        }
        .pager {
            display: inline-block;
            padding: 0;
            margin: 0;
        }
        .pager a, .pager span {
            display: inline-block;
            padding: 8px 15px;
            margin: 0 2px;
            border: 1px solid #e5e5e5;
            text-decoration: none;
            color: #333;
            background-color: #fff;
            border-radius: 0;
        }
        .pager a:hover {
            background-color: #f5f5f5;
        }
        .pager .current {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .pager .ellipsis {
            border: none;
            background: transparent;
            color: #999;
            cursor: default;
        }
        .pager .page-jump {
            border: none;
            background: transparent;
            color: #666;
            margin-left: 15px;
        }
        .pager .page-jump input {
            border: 1px solid #e5e5e5;
            padding: 4px 8px;
            border-radius: 3px;
        }
        .pager .page-jump button {
            background-color: #007bff;
            color: white;
            border: 1px solid #007bff;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
        }
        .pager .page-jump button:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }
        .pager .info {
            border: none;
            background: transparent;
            color: #666;
            margin-left: 15px;
        }
    </style>
}


<div>
    <div id="blog" class="r_con_wrap blog plugins_app_box" style="height: 227px;">
       
        <div class="inside_table pt0 radius ">
            <div class="inside_container inside_menu_right clean">
                <h1>博客</h1>
                <div class="inside_menu">
                    <div class="inside_title"><span>分类</span><i></i></div>
                    <div class="inside_body">
                        <ul>
                            <li><a href="/blog" class="">列表</a></li>
                            <li><a href="#" onclick="return false;" class="current">分类</a></li>
                        </ul>
                        <div class="inside_menu_current" style="left: 87px; opacity: 0; visibility: hidden;"></div>
                    </div>
                </div>
            </div>
            <div class="list_menu">
                <ul class="list_menu_button fr">
                    <li><a class="add" href="/Blog/CateEdit">添加</a></li>
                </ul>
                <div class="search_box fl">
                    <form id="w0" method="get" action="/Blog/Cate">
                        <div class="k_input">
                            <input type="text" class="form_input" name="Keyword" value="@Model.Keyword" size="15" autocomplete="off" placeholder="请输入分类名称">
                            <button type="submit" class="search_btn iconfont">&#xe600;</button>
                        </div>
                        @* <input type="button" class="filter_btn" value="筛选"> *@
                        @* <div class="clear"></div> *@
                    </form>
                </div>
            </div>
            <div class="clear"></div>
            
            <!-- 显示操作结果消息 -->
            @* @if (!string.IsNullOrEmpty(TempData["StatusMessage"] as string)) *@
            @* { *@
            @*     var message = TempData["StatusMessage"] as string; *@
            @*     var messageClass = message.StartsWith("成功") ? "alert-success" : *@
            @*                       message.StartsWith("警告") ? "alert-warning" : "alert-danger"; *@
            @*     <div class="alert @messageClass" id="status-message"> *@
            @*         @message *@
            @*     </div> *@
            @*     <script> *@
            @*         // 5秒后自动隐藏消息 *@
            @*         setTimeout(function() { *@
            @*             document.getElementById('status-message').style.display = 'none'; *@
            @*         }, 5000); *@
            @*     </script> *@
            @* } *@

            <!-- 调试信息 -->
            @* <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px; color: #666;"> *@
            @*     调试信息: @debugInfo *@
            @* </div> *@
            
            <!-- 表格容器 -->
            <div id="blog-cate-table-container">
                @Html.AntiForgeryToken()
                @await Html.PartialAsync("_BlogCateTablePartial", Model)
            </div>

            <!-- 分页控件 -->
            <div class="pager_box">
                @if (totalPages > 0)
                {
                    <div class="pager">
                        @{
                            int currentPage = Model.CurrentPage > 0 ? Model.CurrentPage : 1;
                            int startPage = 1;
                            int endPage = totalPages;
                            bool showStartEllipsis = false;
                            bool showEndEllipsis = false;

                            // 分页显示逻辑：当前页前后各显示2页
                            if (totalPages > 7)
                            {
                                if (currentPage <= 4)
                                {
                                    // 当前页在前面，显示 1,2,3,4,5...最后页
                                    startPage = 1;
                                    endPage = 5;
                                    showEndEllipsis = true;
                                }
                                else if (currentPage >= totalPages - 3)
                                {
                                    // 当前页在后面，显示 1...倒数5,倒数4,倒数3,倒数2,最后页
                                    startPage = totalPages - 4;
                                    endPage = totalPages;
                                    showStartEllipsis = true;
                                }
                                else
                                {
                                    // 当前页在中间，显示 1...当前页-2,当前页-1,当前页,当前页+1,当前页+2...最后页
                                    startPage = currentPage - 2;
                                    endPage = currentPage + 2;
                                    showStartEllipsis = true;
                                    showEndEllipsis = true;
                                }
                            }
                        }

                        @* 上一页 *@
                        @if (currentPage > 1)
                        {
                            <a href="@Url.Page("/Cate", new { CurrentPage = currentPage - 1, Keyword = Model.Keyword })" class="prev">上一页</a>
                        }

                        @* 第一页 *@
                        @if (showStartEllipsis)
                        {
                            @if (currentPage == 1)
                            {
                                <span class="current">1</span>
                            }
                            else
                            {
                                <a href="@Url.Page("/Cate", new { CurrentPage = 1, Keyword = Model.Keyword })">1</a>
                            }
                            <span class="ellipsis">...</span>
                        }

                        @* 页码范围 *@
                        @for (int i = startPage; i <= endPage; i++)
                        {
                            if (i == currentPage)
                            {
                                <span class="current">@i</span>
                            }
                            else
                            {
                                <a href="@Url.Page("/Cate", new { CurrentPage = i, Keyword = Model.Keyword })">@i</a>
                            }
                        }

                        @* 最后一页 *@
                        @if (showEndEllipsis)
                        {
                            <span class="ellipsis">...</span>
                            @if (currentPage == totalPages)
                            {
                                <span class="current">@totalPages</span>
                            }
                            else
                            {
                                <a href="@Url.Page("/Cate", new { CurrentPage = totalPages, Keyword = Model.Keyword })">@totalPages</a>
                            }
                        }

                        @* 下一页 *@
                        @if (currentPage < totalPages)
                        {
                            <a href="@Url.Page("/Cate", new { CurrentPage = currentPage + 1, Keyword = Model.Keyword })" class="next">下一页</a>
                        }

                        @* 页码跳转 *@
                        <span class="page-jump">
                            跳转到
                            <input type="number" id="jump-page-input" min="1" max="@totalPages" value="@currentPage" style="width: 50px; text-align: center; margin: 0 5px;">
                            页
                            <button type="button" id="jump-page-btn" style="margin-left: 5px; padding: 4px 8px;">跳转</button>
                        </span>

                        <span class="info">共 @totalCount 条记录，@totalPages 页</span>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- 添加筛选弹窗 -->
<div id="fixed_right">
    <div class="global_container fixed_search_filter" data-width="396">
        <div class="top_title">筛选 <a href="javascript:;" class="close"></a></div>
        <div class="global_form">
            <div class="box_filter">
                <div class="filter_list">
                    <div class="filter_title">分类名称</div>
                    <div class="filter_option">
                        <div class="filter_option_input">
                            <input type="text" class="box_input full_input" name="category_keyword" value="@Model.Keyword" placeholder="请输入分类名称" />
                        </div>
                        <div class="filter_clean">
                            <button>清除</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="rows clean box_button box_submit">
                <div class="input">
                    <input type="button" class="btn_global btn_submit" value="筛选">
                    <input type="button" class="btn_global btn_cancel" value="取消">
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts{
    <!-- 确保加载jQuery和global.js -->
    <script>
        // 确保jQuery已加载
        if (typeof $ === 'undefined') {
      
            document.write('<script src="/assets/js/jquery-1.10.2.js"><\/script>');
        } else {
        
        }

        // 确保global_obj已加载
        if (typeof global_obj === 'undefined') {
      
            window.global_obj = {};
        } else {
      
        }
        
        // 检查win_alert是否存在，如果不存在则添加自定义实现
        if (typeof global_obj.win_alert !== 'function') {

            
            // 实现自定义的win_alert
            global_obj.win_alert = function(tips, callback, type, is_pop, status) {

                
                type = type || 'alert';
                
                // 解析提示信息
                var title = "提示";
                var subtitle = "";
                var confirmBtn = "确定";
                var cancelBtn = "取消";
                
                if (typeof tips === 'object') {
                    title = tips.title || '提示';
                    subtitle = tips.subtitle || '';
                    confirmBtn = tips.confirmBtn || '确定';
                    cancelBtn = tips.cancelBtn || '取消';
                } else {
                    subtitle = tips;
                }
                
                // 唯一ID，避免冲突
                var alertId = 'custom_alert_' + new Date().getTime();
                
                // 创建弹窗HTML
                var html = '<div class="custom-alert-backdrop" id="' + alertId + '">' +
                    '<div class="custom-alert-box">' +
                    '<div class="custom-alert-title">' + title + '</div>' +
                    '<div class="custom-alert-subtitle">' + subtitle + '</div>' +
                    '<div class="custom-alert-buttons">';
                    
                if (type === 'confirm') {
                    html += '<button class="custom-alert-button cancel" data-alert-id="' + alertId + '">' + cancelBtn + '</button>';
                }
                
                html += '<button class="custom-alert-button confirm" data-alert-id="' + alertId + '">' + confirmBtn + '</button>';
                html += '</div></div></div>';
                
                // 添加到页面
                $('body').append(html);
                
                // 使用事件委托绑定按钮事件（更可靠）
                $(document).off('click', '#' + alertId + ' .custom-alert-button'); // 先移除旧监听器
                $(document).on('click', '#' + alertId + ' .custom-alert-button', function() {
                    var $button = $(this);
                    var currentAlertId = $button.data('alert-id');
                    var isConfirm = $button.hasClass('confirm');
                    
            
                    
                    // 移除弹窗
                    $('#' + currentAlertId).remove();
                    
                    // 如果是确认按钮且有回调函数，则执行回调
                    if (isConfirm && typeof callback === 'function') {
                    
                        try {
                            callback();
                        } catch(e) {
                            console.error('执行自定义弹窗回调时出错:', e);
                        }
                    }

                });
                
                return false;
            };
        }
        
        // 添加div_mask实现，如果不存在
        if (typeof global_obj.div_mask !== 'function') {
            global_obj.div_mask = function(remove) {
                if (remove) {
                    $('#div_mask').remove();
                } else {
                    $('body').append('<div id="div_mask" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:999;"></div>');
                }
            };
        }
        
        // 页面加载完成后初始化功能
        $(document).ready(function() {
  
            // 由于frame_obj.dragsort使用$.get()发送请求，我们需要监控jQuery的AJAX请求
            if (typeof $ !== 'undefined' && $.ajaxSetup) {
                // 添加标志位防止重复刷新
                var isRefreshing = false;

                // 使用jQuery的全局AJAX事件来监控请求
                $(document).ajaxComplete(function(event, xhr, settings) {
                    // 检查是否是分类排序接口
                    if (settings.url && settings.url.includes('/manage/plugins/blog/category-v2-order')) {
      

                        // 检查请求是否成功
                        if (xhr.status >= 200 && xhr.status < 300) {
            

                            // 防止重复刷新
                            if (!isRefreshing) {
                                isRefreshing = true;
                     

                                // 延迟一点时间再刷新，确保操作完成
                                setTimeout(function() {
                     
                                    window.location.reload();
                                }, 500);
                            } else {
                      
                            }
                        } else {
                     
                        }
                   
                    }
                });

                // 监控AJAX错误
                $(document).ajaxError(function(event, xhr, settings, thrownError) {
                    if (settings.url && settings.url.includes('/manage/plugins/blog/category-v2-order')) {
                        console.error('❌ 分类排序接口请求失败:');
                        console.error('URL:', settings.url);
                        console.error('状态码:', xhr.status);
                        console.error('错误信息:', thrownError);
                        console.error('响应内容:', xhr.responseText);
                    }
                });

                // 备用方案：重写$.get方法来确保监控
                if (typeof $.get === 'function') {
                    var originalGet = $.get;
                    $.get = function(url, data, success, dataType) {
                        // 检查是否是分类排序接口
                        if (url && url.includes('/manage/plugins/blog/category-v2-order')) {
                
                            // 包装成功回调
                            var wrappedSuccess = function(responseData, textStatus, jqXHR) {
                  

                                // 防止重复刷新
                                if (!isRefreshing) {
                                    isRefreshing = true;

                                    setTimeout(function() {
                                        window.location.reload();
                                    }, 500);
                                }

                                // 如果原来有成功回调，也要执行
                                if (typeof success === 'function') {
                                    success(responseData, textStatus, jqXHR);
                                }
                            };

                            // 调用原始的$.get方法，但使用包装后的成功回调
                            return originalGet.call(this, url, data, wrappedSuccess, dataType);
                        } else {
                            // 非目标接口，直接调用原始方法
                            return originalGet.apply(this, arguments);
                        }
                    };
                }
            } else {
                console.warn('❌ jQuery不可用，无法监控AJAX请求');
            }

            // 筛选功能 - 点击筛选按钮显示筛选弹窗
            $('.filter_btn').on('click', function() {
                // 显示遮罩和筛选弹窗
                $('#fixed_right').addClass('show');
                $('.fixed_search_filter').show();
                $('body').append('<div id="fixed_right_div_mask" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:999;"></div>');
                
                // 设置筛选框中的初始值
                $('input[name="category_keyword"]').val($('input[name="Keyword"]').val());
                
                return false;
            });
            
            // 筛选功能 - 点击清除按钮清空对应输入框
            $('.filter_clean button').on('click', function() {
                var $container = $(this).closest('.filter_option');
                $container.find('input').val('');
            });
            
            // 筛选功能 - 点击取消按钮或关闭按钮关闭筛选弹窗
            $('.fixed_search_filter .btn_cancel, .fixed_search_filter .close').on('click', function() {
                // 关闭筛选弹窗
                $('#fixed_right').removeClass('show');
                $('.fixed_search_filter').hide();
                $('#fixed_right_div_mask').remove();
                return false;
            });
            
            // 筛选功能 - 点击筛选按钮提交筛选表单
            $('.fixed_search_filter .btn_submit').on('click', function() {
                // 获取筛选值
                var keyword = $('input[name="category_keyword"]').val();
                
                // 更新主表单的值
                $('#w0 input[name="Keyword"]').val(keyword);
                
                // 关闭筛选弹窗
                $('#fixed_right').removeClass('show');
                $('.fixed_search_filter').hide();
                $('#fixed_right_div_mask').remove();
                
                // 提交表单
                $('#w0').submit();
                return false;
            });
            
            // 处理分页按钮的点击事件 - 已由Ajax分页脚本接管
            // $('.pager_box .pager a').on('click', function(e) {
            //     e.preventDefault();

            //     // 获取链接的 href 属性
            //     var url = $(this).attr('href');

            //     if (url) {
            //         // 跳转到该URL
            //         window.location.href = url;
            //     }

            //     return false;
            // });

            // 高亮当前页码
            $('.pager_box .pager .current').css({
                'background-color': '#007bff',
                'color': 'white',
                'border-color': '#007bff'
            });

            // 处理页码跳转功能 - 已由Ajax分页脚本接管
            // $('#jump-page-btn').on('click', function() {
            //     var pageInput = $('#jump-page-input');
            //     var pageNum = parseInt(pageInput.val());
            //     var maxPage = parseInt(pageInput.attr('max'));

            //     // 验证页码
            //     if (isNaN(pageNum) || pageNum < 1) {
            //         pageNum = 1;
            //     } else if (pageNum > maxPage) {
            //         pageNum = maxPage;
            //     }

            //     // 构建URL并跳转
            //     var keyword = '@Model.Keyword';
            //     var url = '@Url.Page("/Cate")' + '?CurrentPage=' + pageNum;
            //     if (keyword) {
            //         url += '&Keyword=' + encodeURIComponent(keyword);
            //     }

            //     window.location.href = url;
            // });

            // // 允许在输入框中按回车键跳转
            // $('#jump-page-input').on('keypress', function(e) {
            //     if (e.which === 13) { // Enter键
            //         e.preventDefault();
            //         $('#jump-page-btn').click();
            //     }
            // });
        });
    </script>
    <script src="/assets/js/app/blog-v2.js" ></script>
    <script src="~/businessJs/Blog/blogCateAjaxPagination.js" ></script>
    <script>
        jQuery(function ($) {
            $(document).ready(function () {


                // 初始化博客分类
                if(typeof blog_obj !== 'undefined' && blog_obj.blog_category_init) {
                    blog_obj.blog_category_init();
                }
            });
        });
    </script>
} 