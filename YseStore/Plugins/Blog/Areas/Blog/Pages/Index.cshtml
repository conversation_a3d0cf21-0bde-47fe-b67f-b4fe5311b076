@page "/List"
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using YseStore.Service.Blog
@model Blog.Areas.Blog.Pages.IndexModel
@{
    var totalCount = Model.BlogList?.dataCount ?? 0;
    var pageSize = Model.PageSize;
    int totalPages = Model.BlogList?.pageCount ?? 0;

    // 调试信息
    var debugInfo = $"总记录数: {totalCount}, 每页大小: {pageSize}, 总页数: {totalPages}";
}

@section Styles {
    <link href="~/businessJs/Blog/ajaxPagination.css" rel="stylesheet" />
    <style>
        /* 分页样式 */
        .pager_box {
            margin: 20px 0;
            text-align: center;
        }
        .pager {
            display: inline-block;
            padding: 0;
            margin: 0;
        }
        .pager a, .pager span {
            display: inline-block;
            padding: 8px 15px;
            margin: 0 2px;
            border: 1px solid #e5e5e5;
            text-decoration: none;
            color: #333;
            background-color: #fff;
            border-radius: 0;
        }
        .pager a:hover {
            background-color: #f5f5f5;
        }
        .pager .current {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .pager .ellipsis {
            border: none;
            background: transparent;
            color: #999;
            cursor: default;
        }
        .pager .page-jump {
            border: none;
            background: transparent;
            color: #666;
            margin-left: 15px;
        }
        .pager .page-jump input {
            border: 1px solid #e5e5e5;
            padding: 4px 8px;
            border-radius: 3px;
        }
        .pager .page-jump button {
            background-color: #007bff;
            color: white;
            border: 1px solid #007bff;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
        }
        .pager .page-jump button:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }
        .pager .info {
            border: none;
            background: transparent;
            color: #666;
            margin-left: 15px;
        }
    </style>
}

<div>
    <div id="blog" class="r_con_wrap blog plugins_app_box" style="height: 114px;">
        @* @if (!string.IsNullOrEmpty(Model.StatusMessage)) *@
        @* { *@
        @*     <div class="alert alert-info alert-dismissible" role="alert" *@
        @*          style="margin: 10px; padding: 10px; background-color: #d9edf7; border: 1px solid #bce8f1; border-radius: 4px; color: #31708f;"> *@
        @*         <button type="button" class="close" data-dismiss="alert" aria-label="Close" *@
        @*                 style="float: right; font-size: 21px; font-weight: bold; line-height: 1; color: #000; text-shadow: 0 1px 0 #fff; opacity: 0.2;"> *@
        @*             &times; *@
        @*         </button> *@
        @*         @Model.StatusMessage *@
        @*     </div> *@
        @* } *@
        
        @Html.AntiForgeryToken()
        
   
        <div class="inside_table pt0 radius ">
            <div class="inside_container inside_menu_right clean">
                <h1>博客</h1>
                <div class="inside_menu">
                    <div class="inside_title"><span>列表</span><i></i></div>
                    <div class="inside_body">
                        <ul>
                            <li><a href="javascript:;" class="current">列表</a></li>
                            <li><a href="/blog/cate">分类</a></li>
                        </ul>
                        <div class="inside_menu_current" style="left: 0px;"></div>
                    </div>
                </div>
            </div>
            <ul class="plugins_app_menu">
                <li><a href="/Blog" class="@(string.IsNullOrWhiteSpace(Model.Status) ? "current" : "")">全部</a></li>
                <li><a href="/Blog?status=published" class="@(Model.Status == "published" ? "current" : "")">已发布</a>
                </li>
                <li><a href="/Blog?status=timing" class="@(Model.Status == "timing" ? "current" : "")">定时发布</a></li>
                <li><a href="/Blog?status=draft" class="@(Model.Status == "draft" ? "current" : "")">草稿箱</a></li>
            </ul>
            <div class="list_menu">
                <ul class="list_menu_button fr">
                    <li><a class="add" href="/Blog/Edit">新增博客</a></li>
                </ul>
                <div class="search_box fl">
                    <form id="w0" action="/Blog" method="get">
                        <div class="k_input">
                            <input type="text" class="form_input" name="keyword" value="@Model.Keyword" size="15" autocomplete="off"
                                   placeholder="请输入关键词">
                            <button type="submit" class="search_btn iconfont">&#xe600;</button>
                        </div>
                        <input type="button" class="filter_btn" value="筛选">
                        <div class="clear"></div>
                        <input type="hidden" name="status" value="@Model.Status">
                        <input type="hidden" name="cateId" value="@Model.CateId">
                        <input type="hidden" name="publishTime" value="@Model.PublishTime">
                        <input type="hidden" name="tagId" value="@Model.TagId">
                    </form>
                </div>
                <div class="clear"></div>
                <div class="search_box_selected">
                    @if (!string.IsNullOrEmpty(Model.CateId))
                    {
                        var categoryName = Model.Categories != null && Model.Categories.ContainsKey(Convert.ToInt16(Model.CateId)) 
                            ? Model.Categories[Convert.ToInt16(Model.CateId)] 
                            : Model.CateId;
                        <span class="btn_item_choice current" data-name="cateId"><b>分类: @categoryName</b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.PublishTime))
                    {
                        <span class="btn_item_choice current" data-name="publishTime"><b>发布时间: @Model.PublishTime</b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.TagId))
                    {
                        var tagIds = Model.TagId.Split(',', StringSplitOptions.RemoveEmptyEntries);
                        var tagNames = new List<string>();
                        foreach (var tagIdStr in tagIds)
                        {
                            if (int.TryParse(tagIdStr.Trim(), out int tagIdInt))
                            {
                                var tagName = Model.Tags?.FirstOrDefault(t => t.TId == tagIdInt)?.Name;
                                if (!string.IsNullOrEmpty(tagName))
                                {
                                    tagNames.Add(tagName);
                                }
                            }
                        }
                        if (tagNames.Any())
                        {
                            <span class="btn_item_choice current" data-name="tagId"><b>标签: @string.Join(", ", tagNames)</b><i></i></span>
                        }
                    }
                </div>
            </div>
            <div class="clear"></div>

            <!-- 调试信息 -->
            @* <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px; color: #666;"> *@
            @*     调试信息: @debugInfo *@
            @* </div> *@

            <!-- 表格容器 -->
            <div id="blog-table-container">
                @Html.AntiForgeryToken()
                @await Html.PartialAsync("_BlogTablePartial", Model)
            </div>

            <!-- 分页控件 -->
            <div class="pager_box">
                @if (totalPages > 0)
                {
                    <div class="pager">
                        @{
                            int currentPage = Model.CurrentPage > 0 ? Model.CurrentPage : 1;
                            int startPage = 1;
                            int endPage = totalPages;
                            bool showStartEllipsis = false;
                            bool showEndEllipsis = false;

                            // 分页显示逻辑：当前页前后各显示2页
                            if (totalPages > 7)
                            {
                                if (currentPage <= 4)
                                {
                                    // 当前页在前面，显示 1,2,3,4,5...最后页
                                    startPage = 1;
                                    endPage = 5;
                                    showEndEllipsis = true;
                                }
                                else if (currentPage >= totalPages - 3)
                                {
                                    // 当前页在后面，显示 1...倒数5,倒数4,倒数3,倒数2,最后页
                                    startPage = totalPages - 4;
                                    endPage = totalPages;
                                    showStartEllipsis = true;
                                }
                                else
                                {
                                    // 当前页在中间，显示 1...当前页-2,当前页-1,当前页,当前页+1,当前页+2...最后页
                                    startPage = currentPage - 2;
                                    endPage = currentPage + 2;
                                    showStartEllipsis = true;
                                    showEndEllipsis = true;
                                }
                            }

                            // 创建保留所有现有筛选条件的路由值字典
                            var routeValues = new Dictionary<string, object>
                            {
                                { "status", Model.Status ?? string.Empty },
                                { "keyword", Model.Keyword ?? string.Empty },
                                { "cateId", Model.CateId ?? string.Empty },
                                { "publishTime", Model.PublishTime ?? string.Empty },
                                { "tagId", Model.TagId ?? string.Empty }
                            };
                        }

                        @* 上一页 *@
                        @if (currentPage > 1)
                        {
                            routeValues["CurrentPage"] = currentPage - 1;
                            <a href="@Url.Page("/Index", routeValues)" class="prev">上一页</a>
                        }

                        @* 第一页 *@
                        @if (showStartEllipsis)
                        {
                            routeValues["CurrentPage"] = 1;
                            @if (currentPage == 1)
                            {
                                <span class="current">1</span>
                            }
                            else
                            {
                                <a href="@Url.Page("/Index", routeValues)">1</a>
                            }
                            <span class="ellipsis">...</span>
                        }

                        @* 页码范围 *@
                        @for (int i = startPage; i <= endPage; i++)
                        {
                            if (i == currentPage)
                            {
                                <span class="current">@i</span>
                            }
                            else
                            {
                                routeValues["CurrentPage"] = i;
                                <a href="@Url.Page("/Index", routeValues)">@i</a>
                            }
                        }

                        @* 最后一页 *@
                        @if (showEndEllipsis)
                        {
                            <span class="ellipsis">...</span>
                            routeValues["CurrentPage"] = totalPages;
                            @if (currentPage == totalPages)
                            {
                                <span class="current">@totalPages</span>
                            }
                            else
                            {
                                <a href="@Url.Page("/Index", routeValues)">@totalPages</a>
                            }
                        }

                        @* 下一页 *@
                        @if (currentPage < totalPages)
                        {
                            routeValues["CurrentPage"] = currentPage + 1;
                            <a href="@Url.Page("/Index", routeValues)" class="next">下一页</a>
                        }

                        @* 页码跳转 *@
                        <span class="page-jump">
                            跳转到
                            <input type="number" id="jump-page-input" min="1" max="@totalPages" value="@currentPage" style="width: 50px; text-align: center; margin: 0 5px;">
                            页
                            <button type="button" id="jump-page-btn" style="margin-left: 5px; padding: 4px 8px;">跳转</button>
                        </span>

                        <span class="info">共 @(Model.BlogList?.dataCount ?? 0) 条记录，@totalPages 页</span>
                    </div>
                }
            </div>
        </div>
    </div>
    <div id="fixed_right">
        <div class="global_container fixed_search_filter" data-width="396">
            <div class="top_title">筛选 <a href="javascript:;" class="close"></a></div>
            <div class="global_form">
                <div class="box_filter">
                    <div class="filter_list">
                        <div class="filter_title">分类</div>
                        <div class="filter_option">
                            <div class="filter_option_input">
                                <dl class="box_drop_double edit_box" data-checkbox="0" data-showadd="0">
                                    <dt><input type="text" class="box_input" name="Select" placeholder="请选择或填写"
                                               value="" autocomplete="off"><input type="hidden" name="cateId"
                                                                                  value="@Model.CateId"
                                                                                  class="hidden_value"><input
                                            type="hidden" name="cateIdType" value="" class="hidden_type"></dt>
                                    <dd class="drop_down">
                                        <div class="drop_menu" data-type="Select">
                                            <div class="drop_list"
                                                 data="@(Model.Categories != null ? "[" + string.Join(",", Model.Categories.Select(c => $"{{\"Name\": \"{c.Value}\", \"Value\": {c.Key}, \"Type\": \"blog_new_category\"}}")) + "]" : "[]")">
                                                <!-- 这里的内容由JavaScript动态生成 -->
                                            </div>

                                        </div>
                                    </dd>
                                </dl>
                            </div>
                            <div class="filter_clean">
                                <button>清除</button>
                            </div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">发布时间</div>
                        <div class="filter_option">
                            <div class="filter_option_input">
                                <input name="publishTime" value="@Model.PublishTime" type="text"
                                       class="box_input full_input input_time"
                                       size="55" readonly="" notnull="">
                            </div>
                            <div class="filter_clean">
                                <button>清除</button>
                            </div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">标签</div>
                        <div class="filter_option">
                            <div class="filter_option_input">
                                <dl class="box_drop_double" data-checkbox="1" data-showadd="0">
                                    <dt class="box_checkbox_list">
                                        <div class="select_placeholder">请选择</div>
                                        <div class="select_list"></div>
                                        <input type="text" class="box_input check_input" name="Select" value=""
                                               placeholder="" size="30" maxlength="255" autocomplete="off"><input
                                            type="hidden" name="tagId" value="@Model.TagId" class="hidden_value"><input
                                            type="hidden" name="tagIdType" value="" class="hidden_type"></dt>
                                    <dd class="drop_down" style="display: none;">
                                        <div class="drop_menu" data-type="Select">
                                            <div class="drop_list"
                                                 data="@(Model.Tags != null ? "[" + string.Join(",", Model.Tags.Select(t => $"{{\"Name\": \"{t.Name}\", \"Value\": {t.TId}, \"Type\": \"blog_new_tag\"}}")) + "]" : "[]")">
                                                <!-- 这里的内容由JavaScript动态生成 -->
                                            </div>
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                            <div class="filter_clean">
                                <button>清除</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="button" class="btn_global btn_submit" value="筛选">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

@section Scripts{
    <script src="/assets/js/app/blog-v2.js" ></script>
    <script src="/assets/js/plugin/daterangepicker/moment.min.js" ></script>
    <script src="/assets/js/plugin/daterangepicker/daterangepicker.js" ></script>
    <script src="~/js/blog-index.js" ></script>
    <script src="~/js/Fix/blogFilterFix.js" ></script>
    <script src="~/businessJs/Blog/blogAjaxPagination.js" ></script>
    <script>
        // 修复 JSON 解析错误
        (function () {
            try {
                // 修复 box_drop_double_default 方法
                if (typeof frame_obj !== 'undefined' && frame_obj.box_drop_double_default) {
                    var originalBoxDropDoubleDefault = frame_obj.box_drop_double_default;
                    frame_obj.box_drop_double_default = function (obj) {
                        try {
                            var $Data = obj.find('.drop_list').attr('data');
                            // 检查 $Data 是否为 undefined 或无效的 JSON
                            if (!$Data || $Data === 'undefined' || $Data === '') {
                                obj.find('.drop_list').attr('data', '[]');
                                $Data = '[]';
                            }
                            return originalBoxDropDoubleDefault.apply(this, arguments);
                        } catch (e) {
                            console.warn('忽略了一个 JSON 解析错误 (default):', e);
                            obj.find('.drop_skin').hide();
                            return false;
                        }
                    };
                }

                // 修复 box_drop_double 方法
                if (typeof frame_obj !== 'undefined' && frame_obj.box_drop_double) {
                    var originalBoxDropDouble = frame_obj.box_drop_double;
                    frame_obj.box_drop_double = function () {
                        try {
                            return originalBoxDropDouble.apply(this, arguments);
                        } catch (e) {
                            console.warn('忽略了一个错误:', e);
                            return false;
                        }
                    };
                }
            } catch (e) {
            }
        })();


        jQuery(function ($) {
            // 初始化
            $(document).ready(function () {
                // 初始化博客功能
                if (typeof blog_obj !== 'undefined' && blog_obj.blog_init) {
                    blog_obj.blog_init();
                }

                // 确保框架初始化下拉框
                if (typeof frame_obj !== 'undefined' && frame_obj.box_drop_double) {
                    frame_obj.box_drop_double();
                }
                updateSelectedCount();
                
                // 处理多选标签问题
                handleMultiSelectTags();
                
                // 处理分页按钮的点击事件 - 已由Ajax分页脚本接管
                // $('.pager_box .pager a').on('click', function(e) {
                //     e.preventDefault();

                //     // 获取链接的 href 属性
                //     var url = $(this).attr('href');

                //     if (url) {
                //         // 跳转到该URL
                //         window.location.href = url;
                //     }

                //     return false;
                // });

                // 高亮当前页码
                $('.pager_box .pager .current').css({
                    'background-color': '#007bff',
                    'color': 'white',
                    'border-color': '#007bff'
                });

                // 处理页码跳转功能 - 已由Ajax分页脚本接管
                // $('#jump-page-btn').on('click', function() {
                //     var pageInput = $('#jump-page-input');
                //     var pageNum = parseInt(pageInput.val());
                //     var maxPage = parseInt(pageInput.attr('max'));

                //     // 验证页码
                //     if (isNaN(pageNum) || pageNum < 1) {
                //         pageNum = 1;
                //     } else if (pageNum > maxPage) {
                //         pageNum = maxPage;
                //     }

                //     // 构建URL并跳转，保留所有筛选条件
                //     var params = new URLSearchParams();
                //     params.set('CurrentPage', pageNum);

                //     // 保留现有的筛选条件
                //     var status = '@Model.Status';
                //     var keyword = '@Model.Keyword';
                //     var cateId = '@Model.CateId';
                //     var publishTime = '@Model.PublishTime';
                //     var tagId = '@Model.TagId';

                //     if (status) params.set('status', status);
                //     if (keyword) params.set('keyword', keyword);
                //     if (cateId) params.set('cateId', cateId);
                //     if (publishTime) params.set('publishTime', publishTime);
                //     if (tagId) params.set('tagId', tagId);

                //     var url = '@Url.Page("/Index")' + '?' + params.toString();
                //     window.location.href = url;
                // });

                // // 允许在输入框中按回车键跳转
                // $('#jump-page-input').on('keypress', function(e) {
                //     if (e.which === 13) { // Enter键
                //         e.preventDefault();
                //         $('#jump-page-btn').click();
                //     }
                // });
            });
            
        });
    </script>
}