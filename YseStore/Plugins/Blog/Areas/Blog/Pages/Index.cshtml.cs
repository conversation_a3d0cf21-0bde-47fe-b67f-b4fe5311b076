using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.Blog;
using YseStore.Model.Entities.Blog;
using Newtonsoft.Json;
using YseStore.IService;
using YseStore.Model;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;

namespace Blog.Areas.Blog.Pages
{
    public class IndexModel : PageModel
    {
        private readonly IBlogNewService _blogService;
        private readonly IBlogCategoryService _categoryService;
        private readonly IBlogNewTagsService _tagService;
        private readonly IViewRenderService _viewRenderService;

        [FromQuery(Name = "keyword")]
        public string? Keyword { get; set; }
        [FromQuery(Name = "status")]
        public string? Status { get; set; }
        [FromQuery(Name = "cateId")]
        public string? CateId { get; set; }
        [FromQuery(Name = "publishTime")]
        public string? PublishTime { get; set; }
        [FromQuery(Name = "tagId")]
        public string? TagId { get; set; }
        [FromQuery(Name = "CurrentPage")]
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 当前页码（用于分页显示）
        /// </summary>
        [BindProperty(SupportsGet = true)]
        public int CurrentPage
        {
            get => PageIndex;
            set => PageIndex = value;
        }
        
        [FromQuery(Name = "pageSize")]
        public int PageSize { get; set; } = 10;
        [FromQuery(Name = "orderBy")]
        public string? OrderBy { get; set; }

        /// <summary>
        /// 排序方向，ASC或DESC，默认DESC
        /// </summary>
        [FromQuery(Name = "sortDirection")]
        public string? SortDirection { get; set; } = "DESC";

        [TempData]
        public string? StatusMessage { get; set; }

        public PageModel<BlogNew> BlogList { get; set; }
        public Dictionary<short, string> Categories { get; set; }
        public List<BlogNewTags> Tags { get; set; }

        /// <summary>
        /// 用于接收排序数据的模型
        /// </summary>
        public class OrderItem
        {
            public int blogId { get; set; }
            public int targetOrder { get; set; }
        }

        public IndexModel(IBlogNewService blogService, IBlogCategoryService categoryService, IBlogNewTagsService tagService, IViewRenderService viewRenderService)
        {
            _blogService = blogService;
            _categoryService = categoryService;
            _tagService = tagService;
            _viewRenderService = viewRenderService;
        }

        public async Task OnGetAsync()
        {
            // 获取所有标签
            Tags = await _tagService.GetAllTags();

            // 获取博客文章列表
            BlogList = await _blogService.GetBlogList(
                Keyword,
                Status,
                CateId,
                PublishTime,
                TagId,
                PageIndex,
                PageSize,
                OrderBy ?? "MyOrder",
                SortDirection ?? "DESC");

            // 获取所有分类，用于在页面上显示分类名称
            var categoryList = await _categoryService.Query();
            Categories = categoryList.ToDictionary(c => c.CateId, c => c.Category);
        }

        /// <summary>
        /// 删除单个博客文章（GET请求处理）
        /// </summary>
        /// <param name="id">文章ID</param>
        /// <returns>重定向到列表页</returns>
        public async Task<IActionResult> OnGetDeleteBlogAsync(int id)
        {
            try
            {
                bool result = await _blogService.DeleteBlog(id);
                if (result)
                {
                    StatusMessage = "删除成功";
                }
                else
                {
                    StatusMessage = "删除失败";
                }
            }
            catch (System.Exception ex)
            {
                StatusMessage = $"删除出错：{ex.Message}";
            }

            return RedirectToPage("/Index", new { 
                area = "Blog",
                status = Status,
                keyword = Keyword,
                cateId = CateId,
                publishTime = PublishTime,
                tagId = TagId,
                pageIndex = PageIndex
            });
        }

        /// <summary>
        /// 批量删除博客文章（GET请求处理）
        /// </summary>
        /// <param name="ids">逗号分隔的文章ID字符串</param>
        /// <returns>重定向到列表页</returns>
        public async Task<IActionResult> OnGetDeleteBatchAsync(string ids)
        {
            if (string.IsNullOrEmpty(ids))
            {
                StatusMessage = "请选择要删除的文章";
                return RedirectToPage("/Index", new { area = "Blog" });
            }

            try
            {
                string[] idArray = ids.Split(',');
                int successCount = 0;

                foreach (var idStr in idArray)
                {
                    if (int.TryParse(idStr, out int id))
                    {
                        bool result = await _blogService.DeleteBlog(id);
                        if (result)
                        {
                            successCount++;
                        }
                    }
                }

                if (successCount == idArray.Length)
                {
                    StatusMessage = $"成功删除 {successCount} 篇文章";
                }
                else
                {
                    StatusMessage = $"部分删除成功，共删除 {successCount}/{idArray.Length} 篇文章";
                }
            }
            catch (System.Exception ex)
            {
                StatusMessage = $"删除出错：{ex.Message}";
            }

            return RedirectToPage("/Index", new { 
                area = "Blog",
                status = Status,
                keyword = Keyword,
                cateId = CateId,
                publishTime = PublishTime,
                tagId = TagId,
                pageIndex = PageIndex
            });
        }

        /// <summary>
        /// 设置博客文章置顶
        /// </summary>
        /// <param name="id">文章ID</param>
        /// <returns>操作结果的JSON</returns>
        public async Task<IActionResult> OnPostTopBlogAsync(int id)
        {
            try
            {
                // 直接使用int类型的id，与服务接口保持一致
                bool result = await _blogService.SetBlogTopAsync(id);
                
                if (result)
                {
                    return new JsonResult(new { success = true, message = "置顶成功" });
                }
                else
                {
                    return new JsonResult(new { success = false, message = "置顶失败" });
                }
            }
            catch (System.Exception ex)
            {
                return new JsonResult(new { success = false, message = $"操作出错：{ex.Message}" });
            }
        }

        /// <summary>
        /// 取消博客文章置顶
        /// </summary>
        /// <param name="id">文章ID</param>
        /// <returns>重定向到博客列表页</returns>
        public async Task<IActionResult> OnGetCancelTopBlogAsync(int id)
        {
            try
            {
                // 直接使用int类型的id，与服务接口保持一致

                // 调用取消置顶服务方法
                bool result = await _blogService.CancelBlogTopAsync(id);
                
                if (result)
                {
                    StatusMessage = "取消置顶成功";
                }
                else
                {
                    StatusMessage = "取消置顶失败";
                }
            }
            catch (System.Exception ex)
            {
                StatusMessage = $"操作出错：{ex.Message}";
            }

            return RedirectToPage("/Index", new { 
                area = "Blog",
                status = Status,
                keyword = Keyword,
                cateId = CateId,
                publishTime = PublishTime,
                tagId = TagId,
                pageIndex = PageIndex
            });
        }

        /// <summary>
        /// 更新博客文章排序处理器 - 处理拖拽排序后的顺序更新
        /// </summary>
        /// <param name="orderData">排序数据JSON字符串</param>
        /// <returns>操作结果的JSON</returns>
        public async Task<IActionResult> OnPostUpdateOrderAsync([FromForm] string orderData)
        {
            try
            {
                if (string.IsNullOrEmpty(orderData))
                {
                    return new JsonResult(new { success = false, message = "未接收到排序数据" });
                }

                // 解析排序数据
                var orderItems = JsonConvert.DeserializeObject<List<OrderItem>>(orderData);
                if (orderItems == null || !orderItems.Any())
                {
                    return new JsonResult(new { success = false, message = "排序数据格式无效" });
                }

                // 去除重复的博客ID，保留最后一个排序值
                var distinctOrderItems = orderItems
                    .GroupBy(x => x.blogId)
                    .Select(g => g.Last())
                    .ToList();

                // 调用服务更新排序
                bool success = await _blogService.UpdateBlogsOrder(distinctOrderItems.ToDictionary(x => x.blogId, x => x.targetOrder));

                if (success)
                {
                    return new JsonResult(new { success = true });
                }
                else
                {
                    return new JsonResult(new { success = false, message = "更新排序失败" });
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"更新排序时发生错误: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取表格内容 - 用于AJAX刷新表格
        /// </summary>
        /// <returns>表格部分视图</returns>
        public async Task<IActionResult> OnGetRefreshTableAsync()
        {
            // 获取所有标签
            Tags = await _tagService.GetAllTags();

            // 获取博客文章列表
            BlogList = await _blogService.GetBlogList(
                Keyword,
                Status,
                CateId,
                PublishTime,
                TagId,
                PageIndex,
                PageSize,
                OrderBy ?? "AccTime",
                SortDirection ?? "DESC");

            // 获取所有分类，用于在页面上显示分类名称
            var categoryList = await _categoryService.Query();
            Categories = categoryList.ToDictionary(c => c.CateId, c => c.Category);

            // 返回表格部分视图
            return Partial("_BlogTablePartial", this);
        }

        /// <summary>
        /// Ajax分页接口 - 返回表格HTML内容
        /// </summary>
        /// <returns>表格部分视图的HTML内容</returns>
        public async Task<IActionResult> OnGetAjaxPageAsync()
        {
            try
            {
                // 获取所有标签
                Tags = await _tagService.GetAllTags();

                // 获取博客文章列表
                BlogList = await _blogService.GetBlogList(
                    Keyword,
                    Status,
                    CateId,
                    PublishTime,
                    TagId,
                    PageIndex,
                    PageSize,
                    OrderBy ?? "AccTime",
                    SortDirection ?? "DESC");

                // 获取所有分类，用于在页面上显示分类名称
                var categoryList = await _categoryService.Query();
                Categories = categoryList.ToDictionary(c => c.CateId, c => c.Category);

                // 计算分页信息
                var totalCount = BlogList?.dataCount ?? 0;
                var totalPages = BlogList?.pageCount ?? 0;
                var currentPage = PageIndex > 0 ? PageIndex : 1;

                // 返回包含HTML和分页信息的JSON
                return new JsonResult(new
                {
                    success = true,
                    // 我们将在前端通过另一个请求获取HTML内容
                    needRefresh = true,
                    pagination = new
                    {
                        currentPage = currentPage,
                        totalPages = totalPages,
                        totalCount = totalCount,
                        pageSize = PageSize
                    },
                    filters = new
                    {
                        keyword = Keyword ?? "",
                        status = Status ?? "",
                        cateId = CateId ?? "",
                        publishTime = PublishTime ?? "",
                        tagId = TagId ?? ""
                    }
                });
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    success = false,
                    message = "获取数据失败：" + ex.Message
                });
            }
        }
    }
}
