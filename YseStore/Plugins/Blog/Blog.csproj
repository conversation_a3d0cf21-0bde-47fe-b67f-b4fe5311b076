<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup Label="Globals">
		<SccProjectName>SAK</SccProjectName>
		<SccProvider>SAK</SccProvider>
		<SccAuxPath>SAK</SccAuxPath>
		<SccLocalPath>SAK</SccLocalPath>
	</PropertyGroup>

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<OutputType>Library</OutputType>
		<EnableDynamicLoading>true</EnableDynamicLoading>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\YseStore.Extensions\YseStore.Extensions.csproj" />
		<ProjectReference Include="..\..\YseStore.Model\YseStore.Model.csproj" />
		<ProjectReference Include="..\PluginsBase\PluginsBase.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Pages\Components\" />
	</ItemGroup>

</Project>
